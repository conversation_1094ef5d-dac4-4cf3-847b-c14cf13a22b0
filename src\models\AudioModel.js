/**
 * AudioModel - Handles audio recording, processing, and storage
 */
export class AudioModel {
  constructor() {
    this.mediaRecorder = null;
    this.audioStream = null;
    this.audioChunks = [];
    this.isRecording = false;
    this.recordingStartTime = null;
    this.audioContext = null;
    this.analyser = null;
    this.dataArray = null;
  }

  /**
   * Initialize audio recording with constraints
   */
  async initializeAudio() {
    try {
      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000
        }
      };

      this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // Setup audio context for analysis
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      const source = this.audioContext.createMediaStreamSource(this.audioStream);
      source.connect(this.analyser);
      
      this.analyser.fftSize = 256;
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      // Setup MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      this.setupMediaRecorderEvents();
      
      return true;
    } catch (error) {
      console.error('Error initializing audio:', error);
      throw new Error('Failed to access microphone. Please check permissions.');
    }
  }

  /**
   * Setup MediaRecorder event handlers
   */
  setupMediaRecorderEvents() {
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data);
      }
    };

    this.mediaRecorder.onstop = () => {
      this.processRecording();
    };
  }

  /**
   * Start recording
   */
  startRecording() {
    if (!this.mediaRecorder) {
      throw new Error('Audio not initialized');
    }

    this.audioChunks = [];
    this.recordingStartTime = Date.now();
    this.isRecording = true;
    this.mediaRecorder.start(100); // Collect data every 100ms
  }

  /**
   * Stop recording
   */
  stopRecording() {
    if (this.mediaRecorder && this.isRecording) {
      this.isRecording = false;
      this.mediaRecorder.stop();
    }
  }

  /**
   * Process recorded audio
   */
  async processRecording() {
    const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
    const duration = (Date.now() - this.recordingStartTime) / 1000;
    
    // Convert to WAV format (simplified - in real implementation you'd use Web Audio API)
    const wavBlob = await this.convertToWav(audioBlob);
    
    return {
      blob: wavBlob,
      duration: duration,
      size: wavBlob.size,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Convert audio to WAV format (placeholder implementation)
   */
  async convertToWav(audioBlob) {
    // This is a simplified implementation
    // In a real app, you'd use Web Audio API to properly convert to 16kHz WAV
    return audioBlob;
  }

  /**
   * Get audio analysis data for visualization
   */
  getAudioData() {
    if (this.analyser && this.dataArray) {
      this.analyser.getByteFrequencyData(this.dataArray);
      return this.dataArray;
    }
    return null;
  }

  /**
   * Get recording duration
   */
  getRecordingDuration() {
    if (this.recordingStartTime) {
      return (Date.now() - this.recordingStartTime) / 1000;
    }
    return 0;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
    }
    if (this.audioContext) {
      this.audioContext.close();
    }
    this.isRecording = false;
  }
}
