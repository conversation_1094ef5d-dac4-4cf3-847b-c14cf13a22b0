/**
 * HeroView - Manages the hero section UI
 */
export class HeroView {
  constructor() {
    this.heroSection = document.getElementById('hero');
    this.startRecordingBtn = document.getElementById('start-recording-btn');
    this.onStartRecording = null;
  }

  /**
   * Initialize hero view
   */
  initialize() {
    this.setupEventListeners();
    this.show();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    if (this.startRecordingBtn) {
      this.startRecordingBtn.addEventListener('click', () => {
        if (this.onStartRecording) {
          this.onStartRecording();
        }
      });
    }
  }

  /**
   * Show hero section
   */
  show() {
    if (this.heroSection) {
      this.heroSection.classList.remove('hidden');
    }
  }

  /**
   * Hide hero section
   */
  hide() {
    if (this.heroSection) {
      this.heroSection.classList.add('hidden');
    }
  }

  /**
   * Set start recording callback
   */
  setOnStartRecording(callback) {
    this.onStartRecording = callback;
  }

  /**
   * Update button state
   */
  setButtonState(enabled, text = null) {
    if (this.startRecordingBtn) {
      this.startRecordingBtn.disabled = !enabled;
      if (text) {
        this.startRecordingBtn.textContent = text;
      }
    }
  }

  /**
   * Show loading state
   */
  showLoading() {
    this.setButtonState(false, 'Initializing...');
  }

  /**
   * Show ready state
   */
  showReady() {
    this.setButtonState(true, 'Start Recording');
  }

  /**
   * Show error state
   */
  showError(message) {
    this.setButtonState(false, 'Error');
    
    // Show error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    // Remove any existing error messages
    const existingError = this.heroSection.querySelector('.error-message');
    if (existingError) {
      existingError.remove();
    }
    
    // Add new error message
    this.heroSection.appendChild(errorDiv);
    
    // Auto-hide error after 5 seconds
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.remove();
      }
      this.showReady();
    }, 5000);
  }

  /**
   * Add animation to button
   */
  animateButton() {
    if (this.startRecordingBtn) {
      this.startRecordingBtn.classList.add('pulse');
      setTimeout(() => {
        this.startRecordingBtn.classList.remove('pulse');
      }, 1000);
    }
  }

  /**
   * Update hero content
   */
  updateContent(title, subtitle) {
    const titleElement = this.heroSection.querySelector('h1');
    const subtitleElement = this.heroSection.querySelector('p');
    
    if (titleElement && title) {
      titleElement.textContent = title;
    }
    
    if (subtitleElement && subtitle) {
      subtitleElement.textContent = subtitle;
    }
  }

  /**
   * Get current visibility state
   */
  isVisible() {
    return this.heroSection && !this.heroSection.classList.contains('hidden');
  }
}
