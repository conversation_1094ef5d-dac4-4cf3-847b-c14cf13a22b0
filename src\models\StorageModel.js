/**
 * StorageModel - Handles IndexedDB operations for storing recordings
 */
export class StorageModel {
  constructor() {
    this.dbName = 'AureaVoiceDB';
    this.dbVersion = 1;
    this.db = null;
  }

  /**
   * Initialize IndexedDB
   */
  async initializeDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = (event) => {
        this.db = event.target.result;
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create recordings object store
        if (!db.objectStoreNames.contains('recordings')) {
          const recordingsStore = db.createObjectStore('recordings', { 
            keyPath: 'id',
            autoIncrement: true 
          });
          
          // Create indexes
          recordingsStore.createIndex('uuid', 'uuid', { unique: true });
          recordingsStore.createIndex('date', 'date', { unique: false });
          recordingsStore.createIndex('confidence', 'confidence', { unique: false });
        }
      };
    });
  }

  /**
   * Generate UUID for recording
   */
  generateUUID() {
    return 'rec-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Save recording to IndexedDB
   */
  async saveRecording(recordingData) {
    if (!this.db) {
      await this.initializeDB();
    }

    const uuid = this.generateUUID();
    const recording = {
      uuid: uuid,
      date: new Date().toISOString(),
      name: `Recording ${new Date().toLocaleDateString()}`,
      duration: recordingData.duration,
      size: recordingData.size,
      confidence: recordingData.confidence || 0,
      audioBlob: recordingData.blob,
      sentence: recordingData.sentence || '',
      timestamp: recordingData.timestamp
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['recordings'], 'readwrite');
      const store = transaction.objectStore('recordings');
      const request = store.add(recording);

      request.onsuccess = () => {
        resolve({ ...recording, id: request.result });
      };

      request.onerror = () => {
        reject(new Error('Failed to save recording'));
      };
    });
  }

  /**
   * Get all recordings
   */
  async getAllRecordings() {
    if (!this.db) {
      await this.initializeDB();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['recordings'], 'readonly');
      const store = transaction.objectStore('recordings');
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(new Error('Failed to get recordings'));
      };
    });
  }

  /**
   * Get recording by UUID
   */
  async getRecordingByUUID(uuid) {
    if (!this.db) {
      await this.initializeDB();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['recordings'], 'readonly');
      const store = transaction.objectStore('recordings');
      const index = store.index('uuid');
      const request = index.get(uuid);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(new Error('Failed to get recording'));
      };
    });
  }

  /**
   * Delete recording by ID
   */
  async deleteRecording(id) {
    if (!this.db) {
      await this.initializeDB();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['recordings'], 'readwrite');
      const store = transaction.objectStore('recordings');
      const request = store.delete(id);

      request.onsuccess = () => {
        resolve(true);
      };

      request.onerror = () => {
        reject(new Error('Failed to delete recording'));
      };
    });
  }

  /**
   * Update recording confidence
   */
  async updateRecordingConfidence(uuid, confidence) {
    if (!this.db) {
      await this.initializeDB();
    }

    const recording = await this.getRecordingByUUID(uuid);
    if (!recording) {
      throw new Error('Recording not found');
    }

    recording.confidence = confidence;

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['recordings'], 'readwrite');
      const store = transaction.objectStore('recordings');
      const request = store.put(recording);

      request.onsuccess = () => {
        resolve(recording);
      };

      request.onerror = () => {
        reject(new Error('Failed to update recording'));
      };
    });
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    const recordings = await this.getAllRecordings();
    const totalSize = recordings.reduce((sum, rec) => sum + (rec.size || 0), 0);
    const totalCount = recordings.length;
    
    return {
      totalRecordings: totalCount,
      totalSize: totalSize,
      averageConfidence: totalCount > 0 
        ? recordings.reduce((sum, rec) => sum + (rec.confidence || 0), 0) / totalCount 
        : 0
    };
  }
}
