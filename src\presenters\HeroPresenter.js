/**
 * HeroPresenter - Controls the hero section logic
 */
import { HeroView } from '../views/HeroView.js';

export class HeroPresenter {
  constructor() {
    this.view = new HeroView();
    this.onStartRecording = null;
  }

  /**
   * Initialize hero presenter
   */
  async initialize() {
    try {
      this.view.initialize();
      this.setupCallbacks();
      return true;
    } catch (error) {
      console.error('Failed to initialize hero presenter:', error);
      throw error;
    }
  }

  /**
   * Setup view callbacks
   */
  setupCallbacks() {
    this.view.setOnStartRecording(() => {
      this.handleStartRecording();
    });
  }

  /**
   * Handle start recording button click
   */
  async handleStartRecording() {
    try {
      // Show loading state
      this.view.showLoading();
      
      // Check microphone permissions
      const hasPermission = await this.checkMicrophonePermission();
      
      if (!hasPermission) {
        this.view.showError('Microphone permission is required to record audio.');
        return;
      }

      // Animate button before navigation
      this.view.animateButton();
      
      // Small delay for animation
      setTimeout(() => {
        if (this.onStartRecording) {
          this.onStartRecording();
        }
      }, 500);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      this.view.showError('Failed to start recording. Please try again.');
    }
  }

  /**
   * Check microphone permission
   */
  async checkMicrophonePermission() {
    try {
      // Try to get user media to check permissions
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      // Stop the stream immediately as we're just checking permissions
      stream.getTracks().forEach(track => track.stop());
      
      return true;
    } catch (error) {
      console.error('Microphone permission check failed:', error);
      
      if (error.name === 'NotAllowedError') {
        return false;
      } else if (error.name === 'NotFoundError') {
        throw new Error('No microphone found. Please connect a microphone and try again.');
      } else if (error.name === 'NotSupportedError') {
        throw new Error('Your browser does not support audio recording.');
      } else {
        throw new Error('Failed to access microphone. Please check your browser settings.');
      }
    }
  }

  /**
   * Show hero section
   */
  show() {
    this.view.show();
    this.view.showReady();
  }

  /**
   * Hide hero section
   */
  hide() {
    this.view.hide();
  }

  /**
   * Set start recording callback
   */
  setOnStartRecording(callback) {
    this.onStartRecording = callback;
  }

  /**
   * Show error message
   */
  showError(message) {
    this.view.showError(message);
  }

  /**
   * Update hero content
   */
  updateContent(title, subtitle) {
    this.view.updateContent(title, subtitle);
  }

  /**
   * Check if view is visible
   */
  isVisible() {
    return this.view.isVisible();
  }

  /**
   * Reset hero state
   */
  reset() {
    this.view.showReady();
  }

  /**
   * Handle app state changes
   */
  handleStateChange(state) {
    // Update hero based on app state
    if (state.hasRecordings) {
      this.view.updateContent(
        'AureaVoice',
        'Record your voice and analyze your American accent confidence'
      );
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // No specific cleanup needed for hero presenter
  }
}
