/**
 * ResultsView - Manages the results display UI
 */
export class ResultsView {
  constructor() {
    this.resultsSection = document.getElementById('results-section');
    this.confidencePercentage = document.getElementById('confidence-percentage');
    this.recordingDate = document.getElementById('recording-date');
    this.finalDuration = document.getElementById('final-duration');
    this.finalSize = document.getElementById('final-size');
    this.recordAgainBtn = document.getElementById('record-again-btn');
    this.viewHistoryBtn = document.getElementById('view-history-btn');
    
    // Callbacks
    this.onRecordAgain = null;
    this.onViewHistory = null;
  }

  /**
   * Initialize results view
   */
  initialize() {
    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    if (this.recordAgainBtn) {
      this.recordAgainBtn.addEventListener('click', () => {
        if (this.onRecordAgain) {
          this.onRecordAgain();
        }
      });
    }

    if (this.viewHistoryBtn) {
      this.viewHistoryBtn.addEventListener('click', () => {
        if (this.onViewHistory) {
          this.onViewHistory();
        }
      });
    }
  }

  /**
   * Show results section
   */
  show() {
    if (this.resultsSection) {
      this.resultsSection.classList.remove('hidden');
    }
  }

  /**
   * Hide results section
   */
  hide() {
    if (this.resultsSection) {
      this.resultsSection.classList.add('hidden');
    }
  }

  /**
   * Display analysis results
   */
  displayResults(results) {
    // Update confidence percentage
    if (this.confidencePercentage && results.confidence !== undefined) {
      this.confidencePercentage.textContent = Math.round(results.confidence);
      this.updateConfidenceColor(results.confidence);
    }

    // Update recording details
    if (this.recordingDate && results.date) {
      const date = new Date(results.date);
      this.recordingDate.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    if (this.finalDuration && results.duration !== undefined) {
      this.finalDuration.textContent = this.formatDuration(results.duration);
    }

    if (this.finalSize && results.size !== undefined) {
      this.finalSize.textContent = this.formatSize(results.size);
    }

    // Add animation to confidence display
    this.animateConfidenceDisplay();
  }

  /**
   * Update confidence color based on percentage
   */
  updateConfidenceColor(confidence) {
    const confidenceDisplay = document.getElementById('confidence-display');
    if (!confidenceDisplay) return;

    // Remove existing color classes
    confidenceDisplay.classList.remove('low', 'medium', 'high', 'excellent');

    // Add appropriate color class
    if (confidence >= 90) {
      confidenceDisplay.classList.add('excellent');
    } else if (confidence >= 75) {
      confidenceDisplay.classList.add('high');
    } else if (confidence >= 60) {
      confidenceDisplay.classList.add('medium');
    } else {
      confidenceDisplay.classList.add('low');
    }
  }

  /**
   * Animate confidence display
   */
  animateConfidenceDisplay() {
    const confidenceDisplay = document.getElementById('confidence-display');
    if (confidenceDisplay) {
      confidenceDisplay.classList.add('animate');
      setTimeout(() => {
        confidenceDisplay.classList.remove('animate');
      }, 1000);
    }
  }

  /**
   * Format duration in seconds to MM:SS
   */
  formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Format file size in bytes to human readable format
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * Show loading state
   */
  showLoading() {
    if (this.confidencePercentage) {
      this.confidencePercentage.textContent = '--';
    }
    
    const confidenceLabel = document.querySelector('.confidence-label');
    if (confidenceLabel) {
      confidenceLabel.textContent = 'Analyzing...';
    }
    
    this.disableButtons();
  }

  /**
   * Show error state
   */
  showError(message) {
    if (this.confidencePercentage) {
      this.confidencePercentage.textContent = 'ERR';
    }
    
    const confidenceLabel = document.querySelector('.confidence-label');
    if (confidenceLabel) {
      confidenceLabel.textContent = message || 'Analysis failed';
    }
    
    this.enableButtons();
  }

  /**
   * Show success state
   */
  showSuccess() {
    const confidenceLabel = document.querySelector('.confidence-label');
    if (confidenceLabel) {
      confidenceLabel.textContent = 'American Accent Confidence';
    }
    
    this.enableButtons();
  }

  /**
   * Disable action buttons
   */
  disableButtons() {
    if (this.recordAgainBtn) {
      this.recordAgainBtn.disabled = true;
    }
    if (this.viewHistoryBtn) {
      this.viewHistoryBtn.disabled = true;
    }
  }

  /**
   * Enable action buttons
   */
  enableButtons() {
    if (this.recordAgainBtn) {
      this.recordAgainBtn.disabled = false;
    }
    if (this.viewHistoryBtn) {
      this.viewHistoryBtn.disabled = false;
    }
  }

  /**
   * Add feedback message
   */
  addFeedbackMessage(confidence) {
    let message = '';
    let messageClass = '';

    if (confidence >= 90) {
      message = 'Excellent! Your American accent is very clear.';
      messageClass = 'excellent';
    } else if (confidence >= 75) {
      message = 'Great job! Your pronunciation is quite good.';
      messageClass = 'good';
    } else if (confidence >= 60) {
      message = 'Good effort! Keep practicing to improve.';
      messageClass = 'fair';
    } else {
      message = 'Keep practicing! Focus on pronunciation clarity.';
      messageClass = 'needs-improvement';
    }

    // Remove existing feedback
    const existingFeedback = this.resultsSection.querySelector('.feedback-message');
    if (existingFeedback) {
      existingFeedback.remove();
    }

    // Add new feedback
    const feedbackDiv = document.createElement('div');
    feedbackDiv.className = `feedback-message ${messageClass}`;
    feedbackDiv.textContent = message;
    
    const resultsContainer = this.resultsSection.querySelector('.results-container');
    if (resultsContainer) {
      const actionButtons = resultsContainer.querySelector('.action-buttons');
      if (actionButtons) {
        resultsContainer.insertBefore(feedbackDiv, actionButtons);
      } else {
        resultsContainer.appendChild(feedbackDiv);
      }
    }
  }

  /**
   * Set callback functions
   */
  setCallbacks(callbacks) {
    this.onRecordAgain = callbacks.onRecordAgain;
    this.onViewHistory = callbacks.onViewHistory;
  }

  /**
   * Reset view state
   */
  reset() {
    if (this.confidencePercentage) {
      this.confidencePercentage.textContent = '--';
    }
    
    if (this.recordingDate) {
      this.recordingDate.textContent = '--';
    }
    
    if (this.finalDuration) {
      this.finalDuration.textContent = '--';
    }
    
    if (this.finalSize) {
      this.finalSize.textContent = '--';
    }

    // Remove feedback message
    const feedbackMessage = this.resultsSection.querySelector('.feedback-message');
    if (feedbackMessage) {
      feedbackMessage.remove();
    }

    // Reset confidence display classes
    const confidenceDisplay = document.getElementById('confidence-display');
    if (confidenceDisplay) {
      confidenceDisplay.classList.remove('low', 'medium', 'high', 'excellent', 'animate');
    }
  }

  /**
   * Get current visibility state
   */
  isVisible() {
    return this.resultsSection && !this.resultsSection.classList.contains('hidden');
  }
}
