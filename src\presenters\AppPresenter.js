/**
 * AppPresenter - Main application controller that coordinates all other presenters
 */
import { HeroPresenter } from './HeroPresenter.js';
import { RecordingPresenter } from './RecordingPresenter.js';
import { ResultsPresenter } from './ResultsPresenter.js';
import { HistoryPresenter } from './HistoryPresenter.js';

export class AppPresenter {
  constructor() {
    this.currentPresenter = null;
    this.presenters = {};
    this.appState = {
      currentView: 'hero',
      isInitialized: false
    };
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      // Initialize all presenters
      this.presenters.hero = new HeroPresenter();
      this.presenters.recording = new RecordingPresenter();
      this.presenters.results = new ResultsPresenter();
      this.presenters.history = new HistoryPresenter();

      // Set up navigation callbacks
      this.setupNavigationCallbacks();

      // Initialize all presenters
      await Promise.all([
        this.presenters.hero.initialize(),
        this.presenters.recording.initialize(),
        this.presenters.results.initialize(),
        this.presenters.history.initialize()
      ]);

      // Show hero section by default
      this.navigateToHero();
      
      this.appState.isInitialized = true;
      console.log('AureaVoice app initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize app:', error);
      this.showError('Failed to initialize application. Please refresh the page.');
    }
  }

  /**
   * Setup navigation callbacks between presenters
   */
  setupNavigationCallbacks() {
    // Hero to Recording
    this.presenters.hero.setOnStartRecording(() => {
      this.navigateToRecording();
    });

    // Recording to Results
    this.presenters.recording.setOnRecordingComplete((recordingData) => {
      this.navigateToResults(recordingData);
    });

    // Results navigation
    this.presenters.results.setCallbacks({
      onRecordAgain: () => this.navigateToRecording(),
      onViewHistory: () => this.navigateToHistory()
    });

    // History navigation
    this.presenters.history.setCallbacks({
      onBackToHome: () => this.navigateToHero(),
      onPlayRecording: (recording) => this.playRecording(recording),
      onDeleteRecording: (recording) => this.deleteRecording(recording)
    });
  }

  /**
   * Navigate to hero section
   */
  navigateToHero() {
    this.hideAllViews();
    this.presenters.hero.show();
    this.currentPresenter = this.presenters.hero;
    this.appState.currentView = 'hero';
  }

  /**
   * Navigate to recording section
   */
  async navigateToRecording() {
    try {
      this.hideAllViews();
      await this.presenters.recording.show();
      this.currentPresenter = this.presenters.recording;
      this.appState.currentView = 'recording';
    } catch (error) {
      console.error('Failed to navigate to recording:', error);
      this.showError('Failed to access microphone. Please check permissions.');
      this.navigateToHero();
    }
  }

  /**
   * Navigate to results section
   */
  async navigateToResults(recordingData) {
    try {
      this.hideAllViews();
      await this.presenters.results.show(recordingData);
      this.currentPresenter = this.presenters.results;
      this.appState.currentView = 'results';
    } catch (error) {
      console.error('Failed to navigate to results:', error);
      this.showError('Failed to process recording results.');
      this.navigateToRecording();
    }
  }

  /**
   * Navigate to history section
   */
  async navigateToHistory() {
    try {
      this.hideAllViews();
      await this.presenters.history.show();
      this.currentPresenter = this.presenters.history;
      this.appState.currentView = 'history';
    } catch (error) {
      console.error('Failed to navigate to history:', error);
      this.showError('Failed to load recording history.');
    }
  }

  /**
   * Hide all views
   */
  hideAllViews() {
    Object.values(this.presenters).forEach(presenter => {
      if (presenter.hide) {
        presenter.hide();
      }
    });
  }

  /**
   * Play a recording from history
   */
  async playRecording(recording) {
    try {
      // Use the recording presenter's waveform to play audio
      await this.presenters.recording.playRecording(recording);
    } catch (error) {
      console.error('Failed to play recording:', error);
      this.showError('Failed to play recording.');
    }
  }

  /**
   * Delete a recording from history
   */
  async deleteRecording(recording) {
    try {
      await this.presenters.history.deleteRecording(recording);
    } catch (error) {
      console.error('Failed to delete recording:', error);
      this.showError('Failed to delete recording.');
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    // Show error in current presenter if available
    if (this.currentPresenter && this.currentPresenter.showError) {
      this.currentPresenter.showError(message);
    } else {
      // Fallback to alert
      alert(message);
    }
  }

  /**
   * Get current app state
   */
  getAppState() {
    return { ...this.appState };
  }

  /**
   * Handle browser back/forward navigation
   */
  handlePopState(event) {
    const state = event.state;
    if (state && state.view) {
      switch (state.view) {
        case 'hero':
          this.navigateToHero();
          break;
        case 'recording':
          this.navigateToRecording();
          break;
        case 'results':
          this.navigateToResults(state.data);
          break;
        case 'history':
          this.navigateToHistory();
          break;
      }
    }
  }

  /**
   * Update browser history
   */
  updateHistory(view, data = null) {
    const state = { view, data };
    const title = `AureaVoice - ${view.charAt(0).toUpperCase() + view.slice(1)}`;
    history.pushState(state, title, `#${view}`);
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    Object.values(this.presenters).forEach(presenter => {
      if (presenter.cleanup) {
        presenter.cleanup();
      }
    });
  }

  /**
   * Handle app visibility change (for cleanup when tab is hidden)
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // Pause any ongoing recordings or playback
      if (this.currentPresenter && this.currentPresenter.pause) {
        this.currentPresenter.pause();
      }
    }
  }
}
