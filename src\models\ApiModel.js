/**
 * ApiModel - Handles API communication for accent detection
 */
export class ApiModel {
  constructor() {
    this.apiBaseUrl = 'http://localhost:8000';
    this.detectEndpoint = '/detect';
  }

  /**
   * Send audio to accent detection API
   */
  async detectAccent(audioBlob, recordingUuid) {
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, `${recordingUuid}.wav`);
      formData.append('uuid', recordingUuid);

      const response = await fetch(`${this.apiBaseUrl}${this.detectEndpoint}`, {
        method: 'POST',
        body: formData,
        headers: {
          // Don't set Content-Type header, let browser set it with boundary for FormData
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      return this.parseApiResponse(result);
    } catch (error) {
      console.error('API Error:', error);
      
      // Return mock data if API is not available (for development)
      if (error.message.includes('fetch')) {
        console.warn('API not available, returning mock data');
        return this.getMockResponse();
      }
      
      throw error;
    }
  }

  /**
   * Parse API response to extract confidence percentage
   */
  parseApiResponse(response) {
    // Expected response format: { "american_accent_confidence": 85.5 }
    // or text format: "American accent confidence: 85%"
    
    let confidence = 0;
    
    if (typeof response === 'object') {
      // JSON response
      confidence = response.american_accent_confidence || 
                  response.confidence || 
                  response.score || 0;
    } else if (typeof response === 'string') {
      // Text response - extract percentage
      const match = response.match(/(\d+(?:\.\d+)?)\s*%/);
      if (match) {
        confidence = parseFloat(match[1]);
      } else {
        // Try to extract number from text
        const numberMatch = response.match(/(\d+(?:\.\d+)?)/);
        if (numberMatch) {
          confidence = parseFloat(numberMatch[1]);
        }
      }
    }

    return {
      confidence: Math.round(confidence * 100) / 100, // Round to 2 decimal places
      rawResponse: response,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get mock response for development/testing
   */
  getMockResponse() {
    // Generate random confidence between 60-95%
    const confidence = Math.random() * 35 + 60;
    
    return {
      confidence: Math.round(confidence * 100) / 100,
      rawResponse: `American accent confidence: ${Math.round(confidence)}%`,
      timestamp: new Date().toISOString(),
      isMock: true
    };
  }

  /**
   * Check API health/availability
   */
  async checkApiHealth() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      return response.ok;
    } catch (error) {
      console.warn('API health check failed:', error);
      return false;
    }
  }

  /**
   * Get API status information
   */
  async getApiStatus() {
    try {
      const isHealthy = await this.checkApiHealth();
      
      return {
        available: isHealthy,
        baseUrl: this.apiBaseUrl,
        endpoint: this.detectEndpoint,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        available: false,
        baseUrl: this.apiBaseUrl,
        endpoint: this.detectEndpoint,
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  /**
   * Batch process multiple recordings
   */
  async batchDetectAccent(recordings) {
    const results = [];
    
    for (const recording of recordings) {
      try {
        const result = await this.detectAccent(recording.audioBlob, recording.uuid);
        results.push({
          uuid: recording.uuid,
          success: true,
          ...result
        });
      } catch (error) {
        results.push({
          uuid: recording.uuid,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }
}
