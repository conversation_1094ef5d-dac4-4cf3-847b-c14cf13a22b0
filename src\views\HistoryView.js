/**
 * HistoryView - Manages the recording history UI
 */
export class HistoryView {
  constructor() {
    this.historySection = document.getElementById('history-section');
    this.historyList = document.getElementById('history-list');
    this.backToHomeBtn = document.getElementById('back-to-home-btn');
    
    // Callbacks
    this.onBackToHome = null;
    this.onPlayRecording = null;
    this.onDeleteRecording = null;
  }

  /**
   * Initialize history view
   */
  initialize() {
    this.setupEventListeners();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    if (this.backToHomeBtn) {
      this.backToHomeBtn.addEventListener('click', () => {
        if (this.onBackToHome) {
          this.onBackToHome();
        }
      });
    }
  }

  /**
   * Show history section
   */
  show() {
    if (this.historySection) {
      this.historySection.classList.remove('hidden');
    }
  }

  /**
   * Hide history section
   */
  hide() {
    if (this.historySection) {
      this.historySection.classList.add('hidden');
    }
  }

  /**
   * Display recordings history
   */
  displayHistory(recordings) {
    if (!this.historyList) return;

    // Clear existing content
    this.historyList.innerHTML = '';

    if (!recordings || recordings.length === 0) {
      this.showEmptyState();
      return;
    }

    // Sort recordings by date (newest first)
    const sortedRecordings = recordings.sort((a, b) => 
      new Date(b.date) - new Date(a.date)
    );

    // Create recording items
    sortedRecordings.forEach(recording => {
      const recordingItem = this.createRecordingItem(recording);
      this.historyList.appendChild(recordingItem);
    });

    // Add summary statistics
    this.addSummaryStats(recordings);
  }

  /**
   * Create a recording item element
   */
  createRecordingItem(recording) {
    const item = document.createElement('div');
    item.className = 'history-item';
    item.dataset.recordingId = recording.id;
    item.dataset.recordingUuid = recording.uuid;

    const date = new Date(recording.date);
    const formattedDate = date.toLocaleDateString();
    const formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    item.innerHTML = `
      <div class="recording-header">
        <div class="recording-info">
          <h3 class="recording-name">${recording.name || 'Unnamed Recording'}</h3>
          <div class="recording-meta">
            <span class="recording-date">${formattedDate} at ${formattedTime}</span>
            <span class="recording-uuid">${recording.uuid}</span>
          </div>
        </div>
        <div class="confidence-badge ${this.getConfidenceClass(recording.confidence)}">
          ${Math.round(recording.confidence || 0)}%
        </div>
      </div>
      
      <div class="recording-details">
        <div class="detail-row">
          <span class="detail-label">Duration:</span>
          <span class="detail-value">${this.formatDuration(recording.duration)}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Size:</span>
          <span class="detail-value">${this.formatSize(recording.size)}</span>
        </div>
        ${recording.sentence ? `
        <div class="detail-row">
          <span class="detail-label">Sentence:</span>
          <span class="detail-value sentence-text">"${recording.sentence}"</span>
        </div>
        ` : ''}
      </div>
      
      <div class="recording-actions">
        <button class="action-btn play-btn" data-action="play">
          <span class="btn-icon">▶️</span>
          Play
        </button>
        <button class="action-btn delete-btn" data-action="delete">
          <span class="btn-icon">🗑️</span>
          Delete
        </button>
      </div>
    `;

    // Add event listeners for actions
    this.setupRecordingItemEvents(item, recording);

    return item;
  }

  /**
   * Setup event listeners for recording item
   */
  setupRecordingItemEvents(item, recording) {
    const playBtn = item.querySelector('.play-btn');
    const deleteBtn = item.querySelector('.delete-btn');

    if (playBtn) {
      playBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        if (this.onPlayRecording) {
          this.onPlayRecording(recording);
        }
      });
    }

    if (deleteBtn) {
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.showDeleteConfirmation(recording);
      });
    }
  }

  /**
   * Show delete confirmation dialog
   */
  showDeleteConfirmation(recording) {
    const confirmed = confirm(
      `Are you sure you want to delete this recording?\n\n` +
      `Date: ${new Date(recording.date).toLocaleString()}\n` +
      `Confidence: ${Math.round(recording.confidence || 0)}%\n\n` +
      `This action cannot be undone.`
    );

    if (confirmed && this.onDeleteRecording) {
      this.onDeleteRecording(recording);
    }
  }

  /**
   * Show empty state when no recordings
   */
  showEmptyState() {
    this.historyList.innerHTML = `
      <div class="empty-state">
        <div class="empty-icon">🎤</div>
        <h3>No recordings yet</h3>
        <p>Start recording to see your history here.</p>
      </div>
    `;
  }

  /**
   * Add summary statistics
   */
  addSummaryStats(recordings) {
    const totalRecordings = recordings.length;
    const averageConfidence = totalRecordings > 0 
      ? recordings.reduce((sum, r) => sum + (r.confidence || 0), 0) / totalRecordings 
      : 0;
    const totalDuration = recordings.reduce((sum, r) => sum + (r.duration || 0), 0);
    const totalSize = recordings.reduce((sum, r) => sum + (r.size || 0), 0);

    const statsDiv = document.createElement('div');
    statsDiv.className = 'history-stats';
    statsDiv.innerHTML = `
      <h3>Summary Statistics</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-value">${totalRecordings}</span>
          <span class="stat-label">Total Recordings</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${Math.round(averageConfidence)}%</span>
          <span class="stat-label">Average Confidence</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${this.formatDuration(totalDuration)}</span>
          <span class="stat-label">Total Duration</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">${this.formatSize(totalSize)}</span>
          <span class="stat-label">Total Size</span>
        </div>
      </div>
    `;

    this.historyList.appendChild(statsDiv);
  }

  /**
   * Get confidence class for styling
   */
  getConfidenceClass(confidence) {
    if (confidence >= 90) return 'excellent';
    if (confidence >= 75) return 'high';
    if (confidence >= 60) return 'medium';
    return 'low';
  }

  /**
   * Format duration in seconds to MM:SS
   */
  formatDuration(seconds) {
    if (!seconds) return '00:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Format file size in bytes to human readable format
   */
  formatSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * Remove recording item from display
   */
  removeRecordingItem(recordingId) {
    const item = this.historyList.querySelector(`[data-recording-id="${recordingId}"]`);
    if (item) {
      item.remove();
      
      // Check if list is now empty
      const remainingItems = this.historyList.querySelectorAll('.history-item');
      if (remainingItems.length === 0) {
        this.showEmptyState();
      }
    }
  }

  /**
   * Show loading state
   */
  showLoading() {
    if (this.historyList) {
      this.historyList.innerHTML = `
        <div class="loading-state">
          <div class="loading-spinner"></div>
          <p>Loading recordings...</p>
        </div>
      `;
    }
  }

  /**
   * Show error state
   */
  showError(message) {
    if (this.historyList) {
      this.historyList.innerHTML = `
        <div class="error-state">
          <div class="error-icon">⚠️</div>
          <h3>Error loading recordings</h3>
          <p>${message || 'Failed to load recording history.'}</p>
        </div>
      `;
    }
  }

  /**
   * Set callback functions
   */
  setCallbacks(callbacks) {
    this.onBackToHome = callbacks.onBackToHome;
    this.onPlayRecording = callbacks.onPlayRecording;
    this.onDeleteRecording = callbacks.onDeleteRecording;
  }

  /**
   * Get current visibility state
   */
  isVisible() {
    return this.historySection && !this.historySection.classList.contains('hidden');
  }
}
