/**
 * WaveformView - Manages waveform visualization using WaveSurfer.js
 */
import WaveSurfer from 'wavesurfer.js';

export class WaveformView {
  constructor() {
    this.waveformContainer = document.getElementById('waveform');
    this.wavesurfer = null;
    this.isInitialized = false;
    this.animationFrame = null;
  }

  /**
   * Initialize WaveSurfer
   */
  async initialize() {
    if (!this.waveformContainer) {
      throw new Error('Waveform container not found');
    }

    try {
      this.wavesurfer = WaveSurfer.create({
        container: this.waveformContainer,
        waveColor: '#4F4A85',
        progressColor: '#383351',
        cursorColor: '#ffffff',
        barWidth: 2,
        barRadius: 3,
        responsive: true,
        height: 80,
        normalize: true,
        backend: 'WebAudio',
        mediaControls: false
      });

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize WaveSurfer:', error);
      throw error;
    }
  }

  /**
   * Start live visualization for recording
   */
  startLiveVisualization(audioModel) {
    if (!this.isInitialized) {
      console.warn('WaveSurfer not initialized');
      return;
    }

    // Create a simple live visualization
    this.animateLiveWaveform(audioModel);
  }

  /**
   * Animate live waveform during recording
   */
  animateLiveWaveform(audioModel) {
    const animate = () => {
      if (!audioModel.isRecording) {
        return;
      }

      const audioData = audioModel.getAudioData();
      if (audioData) {
        // Convert frequency data to waveform-like visualization
        this.updateLiveWaveform(audioData);
      }

      this.animationFrame = requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * Update live waveform visualization
   */
  updateLiveWaveform(audioData) {
    if (!this.waveformContainer) return;

    // Create a simple canvas-based visualization for live audio
    let canvas = this.waveformContainer.querySelector('.live-canvas');
    if (!canvas) {
      canvas = document.createElement('canvas');
      canvas.className = 'live-canvas';
      canvas.width = this.waveformContainer.offsetWidth;
      canvas.height = 80;
      this.waveformContainer.appendChild(canvas);
    }

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw waveform
    ctx.fillStyle = '#4F4A85';
    const barWidth = width / audioData.length;

    for (let i = 0; i < audioData.length; i++) {
      const barHeight = (audioData[i] / 255) * height;
      const x = i * barWidth;
      const y = (height - barHeight) / 2;
      
      ctx.fillRect(x, y, barWidth - 1, barHeight);
    }
  }

  /**
   * Stop live visualization
   */
  stopLiveVisualization() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    // Remove live canvas
    const liveCanvas = this.waveformContainer?.querySelector('.live-canvas');
    if (liveCanvas) {
      liveCanvas.remove();
    }
  }

  /**
   * Load and display recorded audio
   */
  async loadAudio(audioBlob) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const audioUrl = URL.createObjectURL(audioBlob);
      await this.wavesurfer.load(audioUrl);
      
      // Clean up URL after loading
      this.wavesurfer.on('ready', () => {
        URL.revokeObjectURL(audioUrl);
      });
      
      return true;
    } catch (error) {
      console.error('Failed to load audio:', error);
      throw error;
    }
  }

  /**
   * Play loaded audio
   */
  play() {
    if (this.wavesurfer && this.isInitialized) {
      this.wavesurfer.play();
    }
  }

  /**
   * Pause audio playback
   */
  pause() {
    if (this.wavesurfer && this.isInitialized) {
      this.wavesurfer.pause();
    }
  }

  /**
   * Stop audio playback
   */
  stop() {
    if (this.wavesurfer && this.isInitialized) {
      this.wavesurfer.stop();
    }
  }

  /**
   * Clear waveform
   */
  clear() {
    if (this.wavesurfer && this.isInitialized) {
      this.wavesurfer.empty();
    }
    
    // Remove live canvas if exists
    const liveCanvas = this.waveformContainer?.querySelector('.live-canvas');
    if (liveCanvas) {
      liveCanvas.remove();
    }
  }

  /**
   * Show waveform container
   */
  show() {
    if (this.waveformContainer) {
      this.waveformContainer.style.display = 'block';
    }
  }

  /**
   * Hide waveform container
   */
  hide() {
    if (this.waveformContainer) {
      this.waveformContainer.style.display = 'none';
    }
  }

  /**
   * Get audio duration
   */
  getDuration() {
    if (this.wavesurfer && this.isInitialized) {
      return this.wavesurfer.getDuration();
    }
    return 0;
  }

  /**
   * Get current playback position
   */
  getCurrentTime() {
    if (this.wavesurfer && this.isInitialized) {
      return this.wavesurfer.getCurrentTime();
    }
    return 0;
  }

  /**
   * Set playback position
   */
  seekTo(position) {
    if (this.wavesurfer && this.isInitialized) {
      this.wavesurfer.seekTo(position);
    }
  }

  /**
   * Set volume
   */
  setVolume(volume) {
    if (this.wavesurfer && this.isInitialized) {
      this.wavesurfer.setVolume(volume);
    }
  }

  /**
   * Destroy WaveSurfer instance
   */
  destroy() {
    this.stopLiveVisualization();
    
    if (this.wavesurfer) {
      this.wavesurfer.destroy();
      this.wavesurfer = null;
    }
    
    this.isInitialized = false;
  }

  /**
   * Check if WaveSurfer is ready
   */
  isReady() {
    return this.isInitialized && this.wavesurfer;
  }
}
