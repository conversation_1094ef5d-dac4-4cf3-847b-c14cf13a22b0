/**
 * HistoryPresenter - Controls the recording history logic
 */
import { HistoryView } from '../views/HistoryView.js';
import { StorageModel } from '../models/StorageModel.js';

export class HistoryPresenter {
  constructor() {
    this.view = new HistoryView();
    this.storageModel = new StorageModel();
    this.recordings = [];
    this.callbacks = {};
  }

  /**
   * Initialize history presenter
   */
  async initialize() {
    try {
      this.view.initialize();
      await this.storageModel.initialize();
      this.setupCallbacks();
      return true;
    } catch (error) {
      console.error('Failed to initialize history presenter:', error);
      throw error;
    }
  }

  /**
   * Setup view callbacks
   */
  setupCallbacks() {
    this.view.setCallbacks({
      onBackToHome: () => {
        if (this.callbacks.onBackToHome) {
          this.callbacks.onBackToHome();
        }
      },
      onPlayRecording: (recording) => this.playRecording(recording),
      onDeleteRecording: (recording) => this.deleteRecording(recording)
    });
  }

  /**
   * Show history section
   */
  async show() {
    try {
      this.view.show();
      this.view.showLoading();
      
      // Load recordings from storage
      await this.loadRecordings();
      
      // Display recordings
      this.view.displayHistory(this.recordings);
      
    } catch (error) {
      console.error('Failed to show history:', error);
      this.view.showError('Failed to load recording history.');
    }
  }

  /**
   * Hide history section
   */
  hide() {
    this.view.hide();
  }

  /**
   * Load recordings from storage
   */
  async loadRecordings() {
    try {
      this.recordings = await this.storageModel.getAllRecordings();
      console.log(`Loaded ${this.recordings.length} recordings from storage`);
    } catch (error) {
      console.error('Failed to load recordings:', error);
      this.recordings = [];
      throw error;
    }
  }

  /**
   * Play a recording
   */
  async playRecording(recording) {
    try {
      if (this.callbacks.onPlayRecording) {
        await this.callbacks.onPlayRecording(recording);
      }
    } catch (error) {
      console.error('Failed to play recording:', error);
      this.view.showError('Failed to play recording.');
    }
  }

  /**
   * Delete a recording
   */
  async deleteRecording(recording) {
    try {
      // Delete from storage
      await this.storageModel.deleteRecording(recording.uuid);
      
      // Remove from local array
      this.recordings = this.recordings.filter(r => r.uuid !== recording.uuid);
      
      // Update view
      this.view.removeRecordingItem(recording.id);
      
      // If no recordings left, show empty state
      if (this.recordings.length === 0) {
        this.view.displayHistory([]);
      }
      
      console.log(`Deleted recording: ${recording.uuid}`);
      
    } catch (error) {
      console.error('Failed to delete recording:', error);
      this.view.showError('Failed to delete recording.');
    }
  }

  /**
   * Refresh recordings list
   */
  async refresh() {
    try {
      this.view.showLoading();
      await this.loadRecordings();
      this.view.displayHistory(this.recordings);
    } catch (error) {
      console.error('Failed to refresh recordings:', error);
      this.view.showError('Failed to refresh recording list.');
    }
  }

  /**
   * Get recording statistics
   */
  getStatistics() {
    if (this.recordings.length === 0) {
      return {
        totalRecordings: 0,
        averageConfidence: 0,
        totalDuration: 0,
        totalSize: 0,
        bestConfidence: 0,
        worstConfidence: 0
      };
    }

    const confidences = this.recordings.map(r => r.confidence || 0);
    const durations = this.recordings.map(r => r.duration || 0);
    const sizes = this.recordings.map(r => r.size || 0);

    return {
      totalRecordings: this.recordings.length,
      averageConfidence: confidences.reduce((a, b) => a + b, 0) / confidences.length,
      totalDuration: durations.reduce((a, b) => a + b, 0),
      totalSize: sizes.reduce((a, b) => a + b, 0),
      bestConfidence: Math.max(...confidences),
      worstConfidence: Math.min(...confidences)
    };
  }

  /**
   * Filter recordings by confidence level
   */
  filterByConfidence(minConfidence, maxConfidence) {
    const filtered = this.recordings.filter(recording => {
      const confidence = recording.confidence || 0;
      return confidence >= minConfidence && confidence <= maxConfidence;
    });
    
    this.view.displayHistory(filtered);
  }

  /**
   * Filter recordings by date range
   */
  filterByDateRange(startDate, endDate) {
    const filtered = this.recordings.filter(recording => {
      const recordingDate = new Date(recording.date);
      return recordingDate >= startDate && recordingDate <= endDate;
    });
    
    this.view.displayHistory(filtered);
  }

  /**
   * Search recordings by sentence text
   */
  searchBySentence(searchTerm) {
    const filtered = this.recordings.filter(recording => {
      const sentence = recording.sentence || '';
      return sentence.toLowerCase().includes(searchTerm.toLowerCase());
    });
    
    this.view.displayHistory(filtered);
  }

  /**
   * Sort recordings
   */
  sortRecordings(sortBy, order = 'desc') {
    const sorted = [...this.recordings].sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'confidence':
          aValue = a.confidence || 0;
          bValue = b.confidence || 0;
          break;
        case 'duration':
          aValue = a.duration || 0;
          bValue = b.duration || 0;
          break;
        case 'size':
          aValue = a.size || 0;
          bValue = b.size || 0;
          break;
        default:
          return 0;
      }
      
      if (order === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    this.view.displayHistory(sorted);
  }

  /**
   * Export all recordings data
   */
  async exportAllRecordings() {
    try {
      const exportData = {
        exportDate: new Date().toISOString(),
        totalRecordings: this.recordings.length,
        statistics: this.getStatistics(),
        recordings: this.recordings.map(recording => ({
          uuid: recording.uuid,
          name: recording.name,
          confidence: recording.confidence,
          sentence: recording.sentence,
          difficulty: recording.difficulty,
          duration: recording.duration,
          size: recording.size,
          date: recording.date
        }))
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `aurea-voice-history-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Failed to export recordings:', error);
      this.view.showError('Failed to export recording history.');
    }
  }

  /**
   * Clear all recordings
   */
  async clearAllRecordings() {
    try {
      const confirmed = confirm(
        `Are you sure you want to delete all ${this.recordings.length} recordings?\n\n` +
        'This action cannot be undone.'
      );
      
      if (!confirmed) {
        return;
      }
      
      // Delete all recordings from storage
      await Promise.all(
        this.recordings.map(recording => 
          this.storageModel.deleteRecording(recording.uuid)
        )
      );
      
      // Clear local array
      this.recordings = [];
      
      // Update view
      this.view.displayHistory([]);
      
      console.log('All recordings cleared');
      
    } catch (error) {
      console.error('Failed to clear recordings:', error);
      this.view.showError('Failed to clear recording history.');
    }
  }

  /**
   * Set callback functions
   */
  setCallbacks(callbacks) {
    this.callbacks = callbacks;
  }

  /**
   * Show error message
   */
  showError(message) {
    this.view.showError(message);
  }

  /**
   * Check if view is visible
   */
  isVisible() {
    return this.view.isVisible();
  }

  /**
   * Get recordings count
   */
  getRecordingsCount() {
    return this.recordings.length;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.recordings = [];
  }
}
