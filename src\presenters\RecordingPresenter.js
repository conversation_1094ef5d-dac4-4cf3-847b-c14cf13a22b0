/**
 * RecordingPresenter - Controls the recording interface logic
 */
import { RecordingView } from '../views/RecordingView.js';
import { WaveformView } from '../views/WaveformView.js';
import { AudioModel } from '../models/AudioModel.js';
import { SentenceModel } from '../models/SentenceModel.js';
import { StorageModel } from '../models/StorageModel.js';
import { ApiModel } from '../models/ApiModel.js';

export class RecordingPresenter {
  constructor() {
    this.recordingView = new RecordingView();
    this.waveformView = new WaveformView();
    this.audioModel = new AudioModel();
    this.sentenceModel = new SentenceModel();
    this.storageModel = new StorageModel();
    this.apiModel = new ApiModel();
    
    this.onRecordingComplete = null;
    this.currentRecording = null;
    this.recordingTimer = null;
    this.recordingStartTime = null;
  }

  /**
   * Initialize recording presenter
   */
  async initialize() {
    try {
      // Initialize views
      this.recordingView.initialize();
      await this.waveformView.initialize();
      
      // Initialize models
      await this.storageModel.initialize();
      
      this.setupCallbacks();
      return true;
    } catch (error) {
      console.error('Failed to initialize recording presenter:', error);
      throw error;
    }
  }

  /**
   * Setup view callbacks
   */
  setupCallbacks() {
    this.recordingView.setCallbacks({
      onStartRecording: () => this.startRecording(),
      onStopRecording: () => this.stopRecording(),
      onPlayRecording: () => this.playCurrentRecording(),
      onNextSentence: () => this.nextSentence(),
      onPreviousSentence: () => this.previousSentence()
    });
  }

  /**
   * Show recording section
   */
  async show() {
    try {
      // Initialize audio if not already done
      if (!this.audioModel.isInitialized) {
        await this.audioModel.initializeAudio();
      }
      
      // Show views
      this.recordingView.show();
      this.waveformView.show();
      
      // Load first sentence
      this.loadCurrentSentence();
      
      // Reset recording state
      this.resetRecordingState();
      
    } catch (error) {
      console.error('Failed to show recording section:', error);
      throw error;
    }
  }

  /**
   * Hide recording section
   */
  hide() {
    this.stopRecording();
    this.recordingView.hide();
    this.waveformView.hide();
  }

  /**
   * Start recording
   */
  async startRecording() {
    try {
      // Start audio recording
      await this.audioModel.startRecording();
      
      // Update UI
      this.recordingView.showRecording();
      this.waveformView.startLiveVisualization(this.audioModel);
      
      // Start timer
      this.recordingStartTime = Date.now();
      this.startRecordingTimer();
      
      console.log('Recording started');
      
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.recordingView.showError('Failed to start recording. Please check microphone permissions.');
    }
  }

  /**
   * Stop recording
   */
  async stopRecording() {
    try {
      if (!this.audioModel.isRecording) {
        return;
      }
      
      // Stop audio recording
      const audioBlob = await this.audioModel.stopRecording();
      
      // Stop timer
      this.stopRecordingTimer();
      
      // Stop waveform visualization
      this.waveformView.stopLiveVisualization();
      
      // Update UI
      this.recordingView.showProcessing();
      
      // Load audio into waveform for playback
      await this.waveformView.loadAudio(audioBlob);
      
      // Process recording
      await this.processRecording(audioBlob);
      
      console.log('Recording stopped and processed');
      
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.recordingView.showError('Failed to process recording.');
    }
  }

  /**
   * Process the recorded audio
   */
  async processRecording(audioBlob) {
    try {
      const currentSentence = this.sentenceModel.getCurrentSentence();
      const duration = this.getRecordingDuration();
      
      // Create recording data
      const recordingData = {
        audioBlob,
        sentence: currentSentence.text,
        difficulty: currentSentence.difficulty,
        duration,
        size: audioBlob.size,
        date: new Date().toISOString()
      };
      
      // Analyze with API
      this.recordingView.showAnalyzing();
      const confidence = await this.apiModel.detectAccent(audioBlob);
      recordingData.confidence = confidence;
      
      // Save to storage
      const savedRecording = await this.storageModel.saveRecording(recordingData);
      
      // Update sentence model with result
      this.sentenceModel.completeCurrentSentence(confidence);
      
      // Store current recording for playback
      this.currentRecording = { ...savedRecording, audioBlob };
      
      // Show completion
      this.recordingView.showComplete();
      
      // Navigate to results after a short delay
      setTimeout(() => {
        if (this.onRecordingComplete) {
          this.onRecordingComplete(this.currentRecording);
        }
      }, 1500);
      
    } catch (error) {
      console.error('Failed to process recording:', error);
      this.recordingView.showError('Failed to analyze recording. Please try again.');
    }
  }

  /**
   * Play current recording
   */
  async playCurrentRecording() {
    try {
      if (this.currentRecording && this.waveformView.isReady()) {
        this.waveformView.play();
      }
    } catch (error) {
      console.error('Failed to play recording:', error);
    }
  }

  /**
   * Play a recording from history
   */
  async playRecording(recording) {
    try {
      // Load recording data from storage
      const audioBlob = await this.storageModel.getRecordingAudio(recording.uuid);
      
      if (audioBlob) {
        await this.waveformView.loadAudio(audioBlob);
        this.waveformView.play();
      }
    } catch (error) {
      console.error('Failed to play recording from history:', error);
    }
  }

  /**
   * Load current sentence
   */
  loadCurrentSentence() {
    const sentence = this.sentenceModel.getCurrentSentence();
    const progress = this.sentenceModel.getProgress();
    
    this.recordingView.updateSentence(sentence, progress);
  }

  /**
   * Go to next sentence
   */
  nextSentence() {
    this.sentenceModel.nextSentence();
    this.loadCurrentSentence();
    this.resetRecordingState();
  }

  /**
   * Go to previous sentence
   */
  previousSentence() {
    this.sentenceModel.previousSentence();
    this.loadCurrentSentence();
    this.resetRecordingState();
  }

  /**
   * Start recording timer
   */
  startRecordingTimer() {
    this.recordingTimer = setInterval(() => {
      const duration = this.getRecordingDuration();
      const size = this.audioModel.getEstimatedSize();
      this.recordingView.updateRecordingInfo(duration, size);
    }, 100);
  }

  /**
   * Stop recording timer
   */
  stopRecordingTimer() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }

  /**
   * Get recording duration in seconds
   */
  getRecordingDuration() {
    if (this.recordingStartTime) {
      return (Date.now() - this.recordingStartTime) / 1000;
    }
    return 0;
  }

  /**
   * Reset recording state
   */
  resetRecordingState() {
    this.stopRecordingTimer();
    this.recordingStartTime = null;
    this.currentRecording = null;
    this.waveformView.clear();
    this.recordingView.showReady();
  }

  /**
   * Set recording complete callback
   */
  setOnRecordingComplete(callback) {
    this.onRecordingComplete = callback;
  }

  /**
   * Show error message
   */
  showError(message) {
    this.recordingView.showError(message);
  }

  /**
   * Pause any ongoing operations
   */
  pause() {
    if (this.audioModel.isRecording) {
      this.stopRecording();
    }
    this.waveformView.pause();
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopRecordingTimer();
    this.audioModel.cleanup();
    this.waveformView.destroy();
  }
}
