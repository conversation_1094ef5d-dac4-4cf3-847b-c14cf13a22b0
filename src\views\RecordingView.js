/**
 * RecordingView - Manages the recording interface UI
 */
export class RecordingView {
  constructor() {
    this.recordingSection = document.getElementById('recording-section');
    this.sentenceDisplay = document.getElementById('sentence-display');
    this.micButton = document.getElementById('mic-button');
    this.recordingStatus = document.getElementById('recording-status');
    this.recordingDuration = document.getElementById('recording-duration');
    this.recordingSize = document.getElementById('recording-size');
    this.stopRecordingBtn = document.getElementById('stop-recording-btn');
    this.saveRecordingBtn = document.getElementById('save-recording-btn');
    this.nextSentenceBtn = document.getElementById('next-sentence-btn');
    
    // Callbacks
    this.onStartRecording = null;
    this.onStopRecording = null;
    this.onSaveRecording = null;
    this.onNextSentence = null;
    
    // State
    this.isRecording = false;
    this.durationInterval = null;
  }

  /**
   * Initialize recording view
   */
  initialize() {
    this.setupEventListeners();
    this.updateButtonStates();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    if (this.micButton) {
      this.micButton.addEventListener('click', () => {
        if (this.isRecording) {
          this.stopRecording();
        } else {
          this.startRecording();
        }
      });
    }

    if (this.stopRecordingBtn) {
      this.stopRecordingBtn.addEventListener('click', () => {
        this.stopRecording();
      });
    }

    if (this.saveRecordingBtn) {
      this.saveRecordingBtn.addEventListener('click', () => {
        if (this.onSaveRecording) {
          this.onSaveRecording();
        }
      });
    }

    if (this.nextSentenceBtn) {
      this.nextSentenceBtn.addEventListener('click', () => {
        if (this.onNextSentence) {
          this.onNextSentence();
        }
      });
    }
  }

  /**
   * Show recording section
   */
  show() {
    if (this.recordingSection) {
      this.recordingSection.classList.remove('hidden');
    }
  }

  /**
   * Hide recording section
   */
  hide() {
    if (this.recordingSection) {
      this.recordingSection.classList.add('hidden');
    }
  }

  /**
   * Display sentence to read
   */
  displaySentence(sentence) {
    if (this.sentenceDisplay && sentence) {
      this.sentenceDisplay.innerHTML = `
        <div class="sentence-text">${sentence.text}</div>
        <div class="sentence-info">
          <span class="difficulty ${sentence.difficulty}">${sentence.difficulty.toUpperCase()}</span>
          <span class="word-count">${sentence.text.split(' ').length} words</span>
        </div>
      `;
    }
  }

  /**
   * Start recording
   */
  startRecording() {
    this.isRecording = true;
    this.updateRecordingStatus('Recording...');
    this.updateButtonStates();
    this.startDurationTimer();
    
    // Add recording animation
    if (this.micButton) {
      this.micButton.classList.add('recording');
    }
    
    if (this.onStartRecording) {
      this.onStartRecording();
    }
  }

  /**
   * Stop recording
   */
  stopRecording() {
    this.isRecording = false;
    this.updateRecordingStatus('Recording stopped');
    this.updateButtonStates();
    this.stopDurationTimer();
    
    // Remove recording animation
    if (this.micButton) {
      this.micButton.classList.remove('recording');
    }
    
    if (this.onStopRecording) {
      this.onStopRecording();
    }
  }

  /**
   * Update recording status
   */
  updateRecordingStatus(status) {
    if (this.recordingStatus) {
      this.recordingStatus.textContent = status;
    }
  }

  /**
   * Update recording duration display
   */
  updateDuration(seconds) {
    if (this.recordingDuration) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      this.recordingDuration.textContent = 
        `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Update recording size display
   */
  updateSize(bytes) {
    if (this.recordingSize) {
      const kb = Math.round(bytes / 1024);
      this.recordingSize.textContent = `${kb} KB`;
    }
  }

  /**
   * Start duration timer
   */
  startDurationTimer() {
    let seconds = 0;
    this.durationInterval = setInterval(() => {
      seconds++;
      this.updateDuration(seconds);
    }, 1000);
  }

  /**
   * Stop duration timer
   */
  stopDurationTimer() {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
  }

  /**
   * Update button states based on recording state
   */
  updateButtonStates() {
    if (this.stopRecordingBtn) {
      this.stopRecordingBtn.disabled = !this.isRecording;
    }
    
    if (this.saveRecordingBtn) {
      this.saveRecordingBtn.disabled = this.isRecording;
    }
    
    if (this.nextSentenceBtn) {
      this.nextSentenceBtn.disabled = this.isRecording;
    }
    
    // Update mic button appearance
    if (this.micButton) {
      const micIcon = this.micButton.querySelector('.mic-icon');
      if (micIcon) {
        micIcon.textContent = this.isRecording ? '⏹️' : '🎤';
      }
    }
  }

  /**
   * Show processing state
   */
  showProcessing() {
    this.updateRecordingStatus('Processing...');
    this.disableAllButtons();
  }

  /**
   * Show ready state
   */
  showReady() {
    this.updateRecordingStatus('Ready to record');
    this.enableAllButtons();
  }

  /**
   * Disable all buttons
   */
  disableAllButtons() {
    const buttons = [this.micButton, this.stopRecordingBtn, this.saveRecordingBtn, this.nextSentenceBtn];
    buttons.forEach(btn => {
      if (btn) btn.disabled = true;
    });
  }

  /**
   * Enable all buttons
   */
  enableAllButtons() {
    const buttons = [this.micButton, this.stopRecordingBtn, this.saveRecordingBtn, this.nextSentenceBtn];
    buttons.forEach(btn => {
      if (btn) btn.disabled = false;
    });
    this.updateButtonStates(); // Apply proper state logic
  }

  /**
   * Reset view state
   */
  reset() {
    this.isRecording = false;
    this.stopDurationTimer();
    this.updateDuration(0);
    this.updateSize(0);
    this.updateRecordingStatus('Ready to record');
    this.updateButtonStates();
    
    if (this.micButton) {
      this.micButton.classList.remove('recording');
    }
  }

  /**
   * Set callback functions
   */
  setCallbacks(callbacks) {
    this.onStartRecording = callbacks.onStartRecording;
    this.onStopRecording = callbacks.onStopRecording;
    this.onSaveRecording = callbacks.onSaveRecording;
    this.onNextSentence = callbacks.onNextSentence;
  }

  /**
   * Get current visibility state
   */
  isVisible() {
    return this.recordingSection && !this.recordingSection.classList.contains('hidden');
  }
}
