/**
 * ResultsPresenter - Controls the results display logic
 */
import { ResultsView } from '../views/ResultsView.js';
import { WaveformView } from '../views/WaveformView.js';

export class ResultsPresenter {
  constructor() {
    this.view = new ResultsView();
    this.waveformView = new WaveformView();
    this.currentRecording = null;
    this.callbacks = {};
  }

  /**
   * Initialize results presenter
   */
  async initialize() {
    try {
      this.view.initialize();
      await this.waveformView.initialize();
      this.setupCallbacks();
      return true;
    } catch (error) {
      console.error('Failed to initialize results presenter:', error);
      throw error;
    }
  }

  /**
   * Setup view callbacks
   */
  setupCallbacks() {
    this.view.setCallbacks({
      onRecordAgain: () => {
        if (this.callbacks.onRecordAgain) {
          this.callbacks.onRecordAgain();
        }
      },
      onViewHistory: () => {
        if (this.callbacks.onViewHistory) {
          this.callbacks.onViewHistory();
        }
      }
    });
  }

  /**
   * Show results section with recording data
   */
  async show(recordingData) {
    try {
      if (!recordingData) {
        throw new Error('No recording data provided');
      }

      this.currentRecording = recordingData;
      
      // Show views
      this.view.show();
      this.waveformView.show();
      
      // Show loading state initially
      this.view.showLoading();
      
      // Load audio into waveform if available
      if (recordingData.audioBlob) {
        await this.waveformView.loadAudio(recordingData.audioBlob);
      }
      
      // Display results with animation
      await this.displayResults(recordingData);
      
    } catch (error) {
      console.error('Failed to show results:', error);
      this.view.showError('Failed to display results.');
    }
  }

  /**
   * Hide results section
   */
  hide() {
    this.view.hide();
    this.waveformView.hide();
    this.waveformView.stop();
  }

  /**
   * Display recording results
   */
  async displayResults(recordingData) {
    try {
      // Prepare results data
      const results = {
        confidence: recordingData.confidence || 0,
        date: recordingData.date || new Date().toISOString(),
        duration: recordingData.duration || 0,
        size: recordingData.size || 0,
        sentence: recordingData.sentence || '',
        difficulty: recordingData.difficulty || 'medium'
      };

      // Show success state
      this.view.showSuccess();
      
      // Display results with animation delay
      setTimeout(() => {
        this.view.displayResults(results);
        this.view.addFeedbackMessage(results.confidence);
      }, 500);

      // Log analytics
      this.logAnalytics(results);
      
    } catch (error) {
      console.error('Failed to display results:', error);
      this.view.showError('Failed to process results data.');
    }
  }

  /**
   * Play the current recording
   */
  playRecording() {
    try {
      if (this.waveformView.isReady()) {
        this.waveformView.play();
      }
    } catch (error) {
      console.error('Failed to play recording:', error);
    }
  }

  /**
   * Pause recording playback
   */
  pauseRecording() {
    try {
      this.waveformView.pause();
    } catch (error) {
      console.error('Failed to pause recording:', error);
    }
  }

  /**
   * Stop recording playback
   */
  stopRecording() {
    try {
      this.waveformView.stop();
    } catch (error) {
      console.error('Failed to stop recording:', error);
    }
  }

  /**
   * Get confidence level description
   */
  getConfidenceDescription(confidence) {
    if (confidence >= 90) {
      return {
        level: 'Excellent',
        description: 'Your American accent is very clear and natural.',
        color: 'excellent'
      };
    } else if (confidence >= 75) {
      return {
        level: 'Good',
        description: 'Your pronunciation is quite good with minor areas for improvement.',
        color: 'high'
      };
    } else if (confidence >= 60) {
      return {
        level: 'Fair',
        description: 'Good effort! Continue practicing to improve your accent.',
        color: 'medium'
      };
    } else {
      return {
        level: 'Needs Improvement',
        description: 'Keep practicing! Focus on pronunciation clarity and rhythm.',
        color: 'low'
      };
    }
  }

  /**
   * Get improvement suggestions based on confidence
   */
  getImprovementSuggestions(confidence) {
    const suggestions = [];
    
    if (confidence < 90) {
      suggestions.push('Practice with native speaker recordings');
      suggestions.push('Focus on word stress and intonation');
    }
    
    if (confidence < 75) {
      suggestions.push('Work on vowel sounds');
      suggestions.push('Practice consonant clusters');
    }
    
    if (confidence < 60) {
      suggestions.push('Slow down your speech');
      suggestions.push('Focus on individual word pronunciation');
      suggestions.push('Use pronunciation apps for daily practice');
    }
    
    return suggestions;
  }

  /**
   * Log analytics data
   */
  logAnalytics(results) {
    try {
      // Log to console for development
      console.log('Recording Analytics:', {
        confidence: results.confidence,
        duration: results.duration,
        difficulty: results.difficulty,
        timestamp: results.date
      });
      
      // In production, this would send to analytics service
      // analytics.track('recording_completed', results);
      
    } catch (error) {
      console.error('Failed to log analytics:', error);
    }
  }

  /**
   * Export results data
   */
  exportResults() {
    try {
      if (!this.currentRecording) {
        throw new Error('No recording data to export');
      }

      const exportData = {
        confidence: this.currentRecording.confidence,
        sentence: this.currentRecording.sentence,
        difficulty: this.currentRecording.difficulty,
        duration: this.currentRecording.duration,
        date: this.currentRecording.date,
        uuid: this.currentRecording.uuid
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `aurea-voice-result-${this.currentRecording.uuid}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Failed to export results:', error);
      this.view.showError('Failed to export results.');
    }
  }

  /**
   * Set callback functions
   */
  setCallbacks(callbacks) {
    this.callbacks = callbacks;
  }

  /**
   * Show error message
   */
  showError(message) {
    this.view.showError(message);
  }

  /**
   * Check if view is visible
   */
  isVisible() {
    return this.view.isVisible();
  }

  /**
   * Reset results state
   */
  reset() {
    this.view.reset();
    this.waveformView.clear();
    this.currentRecording = null;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopRecording();
    this.waveformView.destroy();
    this.currentRecording = null;
  }
}
