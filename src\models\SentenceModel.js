/**
 * SentenceModel - Manages sample sentences from Common Voice dataset
 */
export class SentenceModel {
  constructor() {
    this.sentences = [
      {
        id: 1,
        text: "The quick brown fox jumps over the lazy dog near the riverbank.",
        difficulty: "easy",
        phonemes: ["th", "qu", "ow", "ks", "mp", "ov", "er", "zy", "og", "ar", "nk"]
      },
      {
        id: 2,
        text: "She sells seashells by the seashore while watching the waves crash.",
        difficulty: "medium",
        phonemes: ["sh", "el", "ls", "ea", "sh", "el", "ls", "by", "th", "ea", "sh", "or", "wh", "le", "wa", "ch", "ng", "wa", "ve", "s", "cr", "ash"]
      },
      {
        id: 3,
        text: "The weather in Massachusetts is particularly challenging for pronunciation practice.",
        difficulty: "hard",
        phonemes: ["th", "we", "th", "er", "in", "ma", "ss", "a", "ch", "u", "se", "tt", "s", "is", "pa", "rt", "ic", "u", "la", "rl", "y", "ch", "al", "le", "ng", "ng", "fo", "r", "pr", "o", "nu", "nc", "ia", "ti", "on", "pr", "ac", "ti", "ce"]
      }
    ];
    
    this.currentSentenceIndex = 0;
    this.completedSentences = [];
  }

  /**
   * Get all available sentences
   */
  getAllSentences() {
    return [...this.sentences];
  }

  /**
   * Get current sentence
   */
  getCurrentSentence() {
    if (this.currentSentenceIndex < this.sentences.length) {
      return this.sentences[this.currentSentenceIndex];
    }
    return null;
  }

  /**
   * Get next sentence
   */
  getNextSentence() {
    this.currentSentenceIndex++;
    return this.getCurrentSentence();
  }

  /**
   * Get previous sentence
   */
  getPreviousSentence() {
    if (this.currentSentenceIndex > 0) {
      this.currentSentenceIndex--;
      return this.getCurrentSentence();
    }
    return null;
  }

  /**
   * Get sentence by ID
   */
  getSentenceById(id) {
    return this.sentences.find(sentence => sentence.id === id);
  }

  /**
   * Mark current sentence as completed
   */
  markCurrentSentenceCompleted(confidence = 0) {
    const currentSentence = this.getCurrentSentence();
    if (currentSentence) {
      const completedSentence = {
        ...currentSentence,
        completedAt: new Date().toISOString(),
        confidence: confidence,
        attempts: this.getAttemptCount(currentSentence.id) + 1
      };
      
      // Remove any previous completion of this sentence
      this.completedSentences = this.completedSentences.filter(
        s => s.id !== currentSentence.id
      );
      
      this.completedSentences.push(completedSentence);
    }
  }

  /**
   * Get completed sentences
   */
  getCompletedSentences() {
    return [...this.completedSentences];
  }

  /**
   * Get attempt count for a sentence
   */
  getAttemptCount(sentenceId) {
    const completed = this.completedSentences.filter(s => s.id === sentenceId);
    return completed.length;
  }

  /**
   * Check if all sentences are completed
   */
  areAllSentencesCompleted() {
    return this.completedSentences.length >= this.sentences.length;
  }

  /**
   * Get progress statistics
   */
  getProgress() {
    const totalSentences = this.sentences.length;
    const completedCount = this.completedSentences.length;
    const averageConfidence = completedCount > 0 
      ? this.completedSentences.reduce((sum, s) => sum + s.confidence, 0) / completedCount 
      : 0;

    return {
      totalSentences,
      completedCount,
      remainingCount: totalSentences - completedCount,
      progressPercentage: (completedCount / totalSentences) * 100,
      averageConfidence: Math.round(averageConfidence * 100) / 100,
      currentSentenceIndex: this.currentSentenceIndex
    };
  }

  /**
   * Reset progress
   */
  resetProgress() {
    this.currentSentenceIndex = 0;
    this.completedSentences = [];
  }

  /**
   * Get random sentence
   */
  getRandomSentence() {
    const randomIndex = Math.floor(Math.random() * this.sentences.length);
    return this.sentences[randomIndex];
  }

  /**
   * Get sentences by difficulty
   */
  getSentencesByDifficulty(difficulty) {
    return this.sentences.filter(sentence => sentence.difficulty === difficulty);
  }

  /**
   * Get sentence statistics
   */
  getSentenceStats(sentenceId) {
    const sentence = this.getSentenceById(sentenceId);
    if (!sentence) return null;

    const attempts = this.getAttemptCount(sentenceId);
    const completed = this.completedSentences.filter(s => s.id === sentenceId);
    const bestConfidence = completed.length > 0 
      ? Math.max(...completed.map(s => s.confidence)) 
      : 0;

    return {
      sentence,
      attempts,
      bestConfidence,
      lastAttempt: completed.length > 0 
        ? completed[completed.length - 1].completedAt 
        : null,
      wordCount: sentence.text.split(' ').length,
      characterCount: sentence.text.length,
      estimatedDuration: Math.ceil(sentence.text.split(' ').length * 0.6) // ~0.6 seconds per word
    };
  }
}
