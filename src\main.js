/**
 * AureaVoice - Voice Recording and American Accent Analysis App
 * Main entry point
 */
import './style.css';
import { AppPresenter } from './presenters/AppPresenter.js';

class AureaVoiceApp {
  constructor() {
    this.appPresenter = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      console.log('Initializing AureaVoice...');

      // Check browser compatibility
      this.checkBrowserCompatibility();

      // Initialize app presenter
      this.appPresenter = new AppPresenter();
      await this.appPresenter.initialize();

      // Setup global event listeners
      this.setupGlobalEventListeners();

      this.isInitialized = true;
      console.log('AureaVoice initialized successfully!');

    } catch (error) {
      console.error('Failed to initialize AureaVoice:', error);
      this.showInitializationError(error);
    }
  }

  /**
   * Check browser compatibility
   */
  checkBrowserCompatibility() {
    const requiredFeatures = [
      'navigator.mediaDevices',
      'navigator.mediaDevices.getUserMedia',
      'window.MediaRecorder',
      'window.indexedDB',
      'window.AudioContext'
    ];

    const missingFeatures = [];

    // Check MediaDevices API
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      missingFeatures.push('Media Devices API');
    }

    // Check MediaRecorder API
    if (!window.MediaRecorder) {
      missingFeatures.push('MediaRecorder API');
    }

    // Check IndexedDB
    if (!window.indexedDB) {
      missingFeatures.push('IndexedDB');
    }

    // Check Web Audio API
    if (!window.AudioContext && !window.webkitAudioContext) {
      missingFeatures.push('Web Audio API');
    }

    if (missingFeatures.length > 0) {
      throw new Error(
        `Your browser doesn't support required features: ${missingFeatures.join(', ')}. ` +
        'Please use a modern browser like Chrome, Firefox, or Safari.'
      );
    }
  }

  /**
   * Setup global event listeners
   */
  setupGlobalEventListeners() {
    // Handle browser back/forward navigation
    window.addEventListener('popstate', (event) => {
      if (this.appPresenter) {
        this.appPresenter.handlePopState(event);
      }
    });

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (this.appPresenter) {
        this.appPresenter.handleVisibilityChange();
      }
    });

    // Handle beforeunload for cleanup
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // Handle errors
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
    });
  }

  /**
   * Show initialization error
   */
  showInitializationError(error) {
    const appElement = document.getElementById('app');
    if (appElement) {
      appElement.innerHTML = `
        <div class="error-container">
          <div class="error-content">
            <h1>🎤 AureaVoice</h1>
            <div class="error-message">
              <h2>Initialization Failed</h2>
              <p>${error.message}</p>
              <button onclick="location.reload()" class="cta-button">
                Try Again
              </button>
            </div>
          </div>
        </div>
      `;
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.appPresenter) {
      this.appPresenter.cleanup();
    }
  }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  const app = new AureaVoiceApp();
  await app.init();
});

// Export for debugging
window.AureaVoice = AureaVoiceApp;
