/* AureaVoice App Styles */
:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Color scheme */
  --primary-color: #4F4A85;
  --secondary-color: #383351;
  --accent-color: #6C63FF;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;

  /* Confidence colors */
  --excellent-color: #28a745;
  --high-color: #17a2b8;
  --medium-color: #ffc107;
  --low-color: #dc3545;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* Border radius */
  --border-radius: 8px;
  --border-radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
  --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: var(--dark-color);
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

/* Button Styles */
button {
  border: none;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cta-button {
  background: var(--accent-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transform: translateY(0);
}

.cta-button:hover:not(:disabled) {
  background: #5a52d5;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.cta-button.pulse {
  animation: pulse 1s ease-in-out;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.action-btn {
  background: var(--light-color);
  color: var(--dark-color);
  border: 2px solid var(--primary-color);
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs);
}

.action-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

/* Hero Section */
.hero-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xxl);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  margin-bottom: var(--spacing-md);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-content p {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.error-message {
  background: var(--danger-color);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-md);
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Recording Section */
.recording-section {
  flex: 1;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.recording-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.recording-container h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  font-size: 2rem;
}

.sentence-display {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xl);
}

.sentence-text {
  font-size: 1.4rem;
  line-height: 1.8;
  color: var(--dark-color);
  margin-bottom: var(--spacing-md);
  font-weight: 500;
}

.sentence-info {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  font-size: 0.9rem;
  color: #666;
}

.difficulty {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
}

.difficulty.easy { background: var(--success-color); color: white; }
.difficulty.medium { background: var(--warning-color); color: white; }
.difficulty.hard { background: var(--danger-color); color: white; }

.recording-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--accent-color);
  color: white;
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.mic-button:hover:not(:disabled) {
  transform: scale(1.1);
  background: #5a52d5;
}

.mic-button.recording {
  background: var(--danger-color);
  animation: recordingPulse 1.5s ease-in-out infinite;
}

@keyframes recordingPulse {
  0%, 100% { transform: scale(1); box-shadow: var(--shadow-lg); }
  50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(220, 53, 69, 0.5); }
}

.recording-status {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--primary-color);
}

.waveform-container {
  background: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.live-canvas {
  width: 100%;
  height: 80px;
  border-radius: var(--border-radius);
}

.recording-info {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--primary-color);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* Results Section */
.results-section {
  flex: 1;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.results-container {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.results-container h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-xl);
  font-size: 2rem;
}

.confidence-display {
  background: white;
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.confidence-display.animate {
  animation: confidenceReveal 1s ease-out;
}

@keyframes confidenceReveal {
  0% { transform: scale(0.8); opacity: 0; }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); opacity: 1; }
}

.confidence-score {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
}

.confidence-label {
  font-size: 1.2rem;
  color: #666;
  font-weight: 500;
}

/* Confidence color classes */
.confidence-display.excellent .confidence-score { color: var(--excellent-color); }
.confidence-display.high .confidence-score { color: var(--high-color); }
.confidence-display.medium .confidence-score { color: var(--medium-color); }
.confidence-display.low .confidence-score { color: var(--low-color); }

.recording-details {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-xl);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-weight: 600;
  color: var(--primary-color);
}

.feedback-message {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  font-weight: 500;
}

.feedback-message.excellent {
  background: rgba(40, 167, 69, 0.1);
  color: var(--excellent-color);
  border: 2px solid var(--excellent-color);
}

.feedback-message.good {
  background: rgba(23, 162, 184, 0.1);
  color: var(--high-color);
  border: 2px solid var(--high-color);
}

.feedback-message.fair {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
  border: 2px solid var(--warning-color);
}

.feedback-message.needs-improvement {
  background: rgba(220, 53, 69, 0.1);
  color: var(--low-color);
  border: 2px solid var(--low-color);
}

/* History Section */
.history-section {
  flex: 1;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.history-container {
  max-width: 900px;
  margin: 0 auto;
}

.history-container h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-xl);
  font-size: 2rem;
  text-align: center;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.history-item {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
}

.history-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.recording-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.recording-info h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
  font-size: 1.2rem;
}

.recording-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: #666;
}

.confidence-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1.1rem;
  min-width: 60px;
  text-align: center;
}

.confidence-badge.excellent { background: var(--excellent-color); color: white; }
.confidence-badge.high { background: var(--high-color); color: white; }
.confidence-badge.medium { background: var(--warning-color); color: white; }
.confidence-badge.low { background: var(--low-color); color: white; }

.recording-details {
  margin-bottom: var(--spacing-md);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xs) 0;
  font-size: 0.9rem;
}

.detail-label {
  font-weight: 600;
  color: var(--primary-color);
}

.sentence-text {
  font-style: italic;
  color: #666;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recording-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

.recording-actions .action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-icon {
  font-size: 1rem;
}

.play-btn {
  background: var(--success-color);
  color: white;
  border: none;
}

.play-btn:hover:not(:disabled) {
  background: #218838;
}

.delete-btn {
  background: var(--danger-color);
  color: white;
  border: none;
}

.delete-btn:hover:not(:disabled) {
  background: #c82333;
}

.history-stats {
  background: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.history-stats h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(79, 74, 133, 0.1);
  border-radius: var(--border-radius);
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* Empty and Error States */
.empty-state, .loading-state, .error-state {
  text-align: center;
  padding: var(--spacing-xxl);
  color: #666;
}

.empty-icon, .error-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content p {
    font-size: 1.1rem;
  }

  .recording-container,
  .results-container,
  .history-container {
    padding: var(--spacing-md);
  }

  .sentence-text {
    font-size: 1.2rem;
  }

  .mic-button {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .confidence-score {
    font-size: 3rem;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 200px;
  }

  .recording-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .recording-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .sentence-text {
    max-width: none;
    white-space: normal;
  }
}

@media (max-width: 480px) {
  .hero-section,
  .recording-section,
  .results-section,
  .history-section {
    padding: var(--spacing-md);
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .sentence-display {
    padding: var(--spacing-md);
  }

  .confidence-display {
    padding: var(--spacing-lg);
  }

  .confidence-score {
    font-size: 2.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .recording-info {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
