{"version": 3, "sources": ["../../wavesurfer.js/dist/wavesurfer.esm.js"], "sourcesContent": ["function t(t,e,i,s){return new(i||(i=Promise))((function(n,r){function o(t){try{h(s.next(t))}catch(t){r(t)}}function a(t){try{h(s.throw(t))}catch(t){r(t)}}function h(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}h((s=s.apply(t,e||[])).next())}))}\"function\"==typeof SuppressedError&&SuppressedError;class e{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==i?void 0:i.once){const i=()=>{this.un(t,i),this.un(t,e)};return this.on(t,i),i}return()=>this.un(t,e)}un(t,e){var i;null===(i=this.listeners[t])||void 0===i||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}const i={decode:function(e,i){return t(this,void 0,void 0,(function*(){const t=new AudioContext({sampleRate:i});return t.decodeAudioData(e).finally((()=>t.close()))}))},createBuffer:function(t,e){return\"number\"==typeof t[0]&&(t=[t]),function(t){const e=t[0];if(e.some((t=>t>1||t<-1))){const i=e.length;let s=0;for(let t=0;t<i;t++){const i=Math.abs(e[t]);i>s&&(s=i)}for(const e of t)for(let t=0;t<i;t++)e[t]/=s}}(t),{duration:e,length:t[0].length,sampleRate:t[0].length/e,numberOfChannels:t.length,getChannelData:e=>null==t?void 0:t[e],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}};function s(t,e){const i=e.xmlns?document.createElementNS(e.xmlns,t):document.createElement(t);for(const[t,n]of Object.entries(e))if(\"children\"===t&&n)for(const[t,e]of Object.entries(n))e instanceof Node?i.appendChild(e):\"string\"==typeof e?i.appendChild(document.createTextNode(e)):i.appendChild(s(t,e));else\"style\"===t?Object.assign(i.style,n):\"textContent\"===t?i.textContent=n:i.setAttribute(t,n.toString());return i}function n(t,e,i){const n=s(t,e||{});return null==i||i.appendChild(n),n}var r=Object.freeze({__proto__:null,createElement:n,default:n});const o={fetchBlob:function(e,i,s){return t(this,void 0,void 0,(function*(){const n=yield fetch(e,s);if(n.status>=400)throw new Error(`Failed to fetch ${e}: ${n.status} (${n.statusText})`);return function(e,i){t(this,void 0,void 0,(function*(){if(!e.body||!e.headers)return;const s=e.body.getReader(),n=Number(e.headers.get(\"Content-Length\"))||0;let r=0;const o=e=>t(this,void 0,void 0,(function*(){r+=(null==e?void 0:e.length)||0;const t=Math.round(r/n*100);i(t)})),a=()=>t(this,void 0,void 0,(function*(){let t;try{t=yield s.read()}catch(t){return}t.done||(o(t.value),yield a())}));a()}))}(n.clone(),i),n.blob()}))}};class a extends e{constructor(t){super(),this.isExternalMedia=!1,t.media?(this.media=t.media,this.isExternalMedia=!0):this.media=document.createElement(\"audio\"),t.mediaControls&&(this.media.controls=!0),t.autoplay&&(this.media.autoplay=!0),null!=t.playbackRate&&this.onMediaEvent(\"canplay\",(()=>{null!=t.playbackRate&&(this.media.playbackRate=t.playbackRate)}),{once:!0})}onMediaEvent(t,e,i){return this.media.addEventListener(t,e,i),()=>this.media.removeEventListener(t,e,i)}getSrc(){return this.media.currentSrc||this.media.src||\"\"}revokeSrc(){const t=this.getSrc();t.startsWith(\"blob:\")&&URL.revokeObjectURL(t)}canPlayType(t){return\"\"!==this.media.canPlayType(t)}setSrc(t,e){const i=this.getSrc();if(t&&i===t)return;this.revokeSrc();const s=e instanceof Blob&&(this.canPlayType(e.type)||!t)?URL.createObjectURL(e):t;i&&this.media.removeAttribute(\"src\");try{this.media.src=s}catch(e){this.media.src=t}}destroy(){this.isExternalMedia||(this.media.pause(),this.media.remove(),this.revokeSrc(),this.media.removeAttribute(\"src\"),this.media.load())}setMediaElement(t){this.media=t}play(){return t(this,void 0,void 0,(function*(){return this.media.play()}))}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(t){this.media.currentTime=Math.max(0,Math.min(t,this.getDuration()))}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(t){this.media.volume=t}getMuted(){return this.media.muted}setMuted(t){this.media.muted=t}getPlaybackRate(){return this.media.playbackRate}isSeeking(){return this.media.seeking}setPlaybackRate(t,e){null!=e&&(this.media.preservesPitch=e),this.media.playbackRate=t}getMediaElement(){return this.media}setSinkId(t){return this.media.setSinkId(t)}}class h extends e{constructor(t,e){super(),this.timeouts=[],this.isScrollable=!1,this.audioData=null,this.resizeObserver=null,this.lastContainerWidth=0,this.isDragging=!1,this.subscriptions=[],this.unsubscribeOnScroll=[],this.subscriptions=[],this.options=t;const i=this.parentFromOptionsContainer(t.container);this.parent=i;const[s,n]=this.initHtml();i.appendChild(s),this.container=s,this.scrollContainer=n.querySelector(\".scroll\"),this.wrapper=n.querySelector(\".wrapper\"),this.canvasWrapper=n.querySelector(\".canvases\"),this.progressWrapper=n.querySelector(\".progress\"),this.cursor=n.querySelector(\".cursor\"),e&&n.appendChild(e),this.initEvents()}parentFromOptionsContainer(t){let e;if(\"string\"==typeof t?e=document.querySelector(t):t instanceof HTMLElement&&(e=t),!e)throw new Error(\"Container not found\");return e}initEvents(){const t=t=>{const e=this.wrapper.getBoundingClientRect(),i=t.clientX-e.left,s=t.clientY-e.top;return[i/e.width,s/e.height]};if(this.wrapper.addEventListener(\"click\",(e=>{const[i,s]=t(e);this.emit(\"click\",i,s)})),this.wrapper.addEventListener(\"dblclick\",(e=>{const[i,s]=t(e);this.emit(\"dblclick\",i,s)})),!0!==this.options.dragToSeek&&\"object\"!=typeof this.options.dragToSeek||this.initDrag(),this.scrollContainer.addEventListener(\"scroll\",(()=>{const{scrollLeft:t,scrollWidth:e,clientWidth:i}=this.scrollContainer,s=t/e,n=(t+i)/e;this.emit(\"scroll\",s,n,t,t+i)})),\"function\"==typeof ResizeObserver){const t=this.createDelay(100);this.resizeObserver=new ResizeObserver((()=>{t().then((()=>this.onContainerResize())).catch((()=>{}))})),this.resizeObserver.observe(this.scrollContainer)}}onContainerResize(){const t=this.parent.clientWidth;t===this.lastContainerWidth&&\"auto\"!==this.options.height||(this.lastContainerWidth=t,this.reRender())}initDrag(){this.subscriptions.push(function(t,e,i,s,n=3,r=0,o=100){if(!t)return()=>{};const a=matchMedia(\"(pointer: coarse)\").matches;let h=()=>{};const l=l=>{if(l.button!==r)return;l.preventDefault(),l.stopPropagation();let d=l.clientX,c=l.clientY,u=!1;const p=Date.now(),m=s=>{if(s.preventDefault(),s.stopPropagation(),a&&Date.now()-p<o)return;const r=s.clientX,h=s.clientY,l=r-d,m=h-c;if(u||Math.abs(l)>n||Math.abs(m)>n){const s=t.getBoundingClientRect(),{left:n,top:o}=s;u||(null==i||i(d-n,c-o),u=!0),e(l,m,r-n,h-o),d=r,c=h}},f=e=>{if(u){const i=e.clientX,n=e.clientY,r=t.getBoundingClientRect(),{left:o,top:a}=r;null==s||s(i-o,n-a)}h()},g=t=>{t.relatedTarget&&t.relatedTarget!==document.documentElement||f(t)},v=t=>{u&&(t.stopPropagation(),t.preventDefault())},b=t=>{u&&t.preventDefault()};document.addEventListener(\"pointermove\",m),document.addEventListener(\"pointerup\",f),document.addEventListener(\"pointerout\",g),document.addEventListener(\"pointercancel\",g),document.addEventListener(\"touchmove\",b,{passive:!1}),document.addEventListener(\"click\",v,{capture:!0}),h=()=>{document.removeEventListener(\"pointermove\",m),document.removeEventListener(\"pointerup\",f),document.removeEventListener(\"pointerout\",g),document.removeEventListener(\"pointercancel\",g),document.removeEventListener(\"touchmove\",b),setTimeout((()=>{document.removeEventListener(\"click\",v,{capture:!0})}),10)}};return t.addEventListener(\"pointerdown\",l),()=>{h(),t.removeEventListener(\"pointerdown\",l)}}(this.wrapper,((t,e,i)=>{this.emit(\"drag\",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))}),(t=>{this.isDragging=!0,this.emit(\"dragstart\",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))}),(t=>{this.isDragging=!1,this.emit(\"dragend\",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))})))}getHeight(t,e){var i;const s=(null===(i=this.audioData)||void 0===i?void 0:i.numberOfChannels)||1;if(null==t)return 128;if(!isNaN(Number(t)))return Number(t);if(\"auto\"===t){const t=this.parent.clientHeight||128;return(null==e?void 0:e.every((t=>!t.overlay)))?t/s:t}return 128}initHtml(){const t=document.createElement(\"div\"),e=t.attachShadow({mode:\"open\"}),i=this.options.cspNonce&&\"string\"==typeof this.options.cspNonce?this.options.cspNonce.replace(/\"/g,\"\"):\"\";return e.innerHTML=`\\n      <style${i?` nonce=\"${i}\"`:\"\"}>\\n        :host {\\n          user-select: none;\\n          min-width: 1px;\\n        }\\n        :host audio {\\n          display: block;\\n          width: 100%;\\n        }\\n        :host .scroll {\\n          overflow-x: auto;\\n          overflow-y: hidden;\\n          width: 100%;\\n          position: relative;\\n        }\\n        :host .noScrollbar {\\n          scrollbar-color: transparent;\\n          scrollbar-width: none;\\n        }\\n        :host .noScrollbar::-webkit-scrollbar {\\n          display: none;\\n          -webkit-appearance: none;\\n        }\\n        :host .wrapper {\\n          position: relative;\\n          overflow: visible;\\n          z-index: 2;\\n        }\\n        :host .canvases {\\n          min-height: ${this.getHeight(this.options.height,this.options.splitChannels)}px;\\n        }\\n        :host .canvases > div {\\n          position: relative;\\n        }\\n        :host canvas {\\n          display: block;\\n          position: absolute;\\n          top: 0;\\n          image-rendering: pixelated;\\n        }\\n        :host .progress {\\n          pointer-events: none;\\n          position: absolute;\\n          z-index: 2;\\n          top: 0;\\n          left: 0;\\n          width: 0;\\n          height: 100%;\\n          overflow: hidden;\\n        }\\n        :host .progress > div {\\n          position: relative;\\n        }\\n        :host .cursor {\\n          pointer-events: none;\\n          position: absolute;\\n          z-index: 5;\\n          top: 0;\\n          left: 0;\\n          height: 100%;\\n          border-radius: 2px;\\n        }\\n      </style>\\n\\n      <div class=\"scroll\" part=\"scroll\">\\n        <div class=\"wrapper\" part=\"wrapper\">\\n          <div class=\"canvases\" part=\"canvases\"></div>\\n          <div class=\"progress\" part=\"progress\"></div>\\n          <div class=\"cursor\" part=\"cursor\"></div>\\n        </div>\\n      </div>\\n    `,[t,e]}setOptions(t){if(this.options.container!==t.container){const e=this.parentFromOptionsContainer(t.container);e.appendChild(this.container),this.parent=e}!0!==t.dragToSeek&&\"object\"!=typeof this.options.dragToSeek||this.initDrag(),this.options=t,this.reRender()}getWrapper(){return this.wrapper}getWidth(){return this.scrollContainer.clientWidth}getScroll(){return this.scrollContainer.scrollLeft}setScroll(t){this.scrollContainer.scrollLeft=t}setScrollPercentage(t){const{scrollWidth:e}=this.scrollContainer,i=e*t;this.setScroll(i)}destroy(){var t,e;this.subscriptions.forEach((t=>t())),this.container.remove(),null===(t=this.resizeObserver)||void 0===t||t.disconnect(),null===(e=this.unsubscribeOnScroll)||void 0===e||e.forEach((t=>t())),this.unsubscribeOnScroll=[]}createDelay(t=10){let e,i;const s=()=>{e&&clearTimeout(e),i&&i()};return this.timeouts.push(s),()=>new Promise(((n,r)=>{s(),i=r,e=setTimeout((()=>{e=void 0,i=void 0,n()}),t)}))}convertColorValues(t){if(!Array.isArray(t))return t||\"\";if(t.length<2)return t[0]||\"\";const e=document.createElement(\"canvas\"),i=e.getContext(\"2d\"),s=e.height*(window.devicePixelRatio||1),n=i.createLinearGradient(0,0,0,s),r=1/(t.length-1);return t.forEach(((t,e)=>{const i=e*r;n.addColorStop(i,t)})),n}getPixelRatio(){return Math.max(1,window.devicePixelRatio||1)}renderBarWaveform(t,e,i,s){const n=t[0],r=t[1]||t[0],o=n.length,{width:a,height:h}=i.canvas,l=h/2,d=this.getPixelRatio(),c=e.barWidth?e.barWidth*d:1,u=e.barGap?e.barGap*d:e.barWidth?c/2:0,p=e.barRadius||0,m=a/(c+u)/o,f=p&&\"roundRect\"in i?\"roundRect\":\"rect\";i.beginPath();let g=0,v=0,b=0;for(let t=0;t<=o;t++){const o=Math.round(t*m);if(o>g){const t=Math.round(v*l*s),n=t+Math.round(b*l*s)||1;let r=l-t;\"top\"===e.barAlign?r=0:\"bottom\"===e.barAlign&&(r=h-n),i[f](g*(c+u),r,c,n,p),g=o,v=0,b=0}const a=Math.abs(n[t]||0),d=Math.abs(r[t]||0);a>v&&(v=a),d>b&&(b=d)}i.fill(),i.closePath()}renderLineWaveform(t,e,i,s){const n=e=>{const n=t[e]||t[0],r=n.length,{height:o}=i.canvas,a=o/2,h=i.canvas.width/r;i.moveTo(0,a);let l=0,d=0;for(let t=0;t<=r;t++){const r=Math.round(t*h);if(r>l){const t=a+(Math.round(d*a*s)||1)*(0===e?-1:1);i.lineTo(l,t),l=r,d=0}const o=Math.abs(n[t]||0);o>d&&(d=o)}i.lineTo(l,a)};i.beginPath(),n(0),n(1),i.fill(),i.closePath()}renderWaveform(t,e,i){if(i.fillStyle=this.convertColorValues(e.waveColor),e.renderFunction)return void e.renderFunction(t,i);let s=e.barHeight||1;if(e.normalize){const e=Array.from(t[0]).reduce(((t,e)=>Math.max(t,Math.abs(e))),0);s=e?1/e:1}e.barWidth||e.barGap||e.barAlign?this.renderBarWaveform(t,e,i,s):this.renderLineWaveform(t,e,i,s)}renderSingleCanvas(t,e,i,s,n,r,o){const a=this.getPixelRatio(),h=document.createElement(\"canvas\");h.width=Math.round(i*a),h.height=Math.round(s*a),h.style.width=`${i}px`,h.style.height=`${s}px`,h.style.left=`${Math.round(n)}px`,r.appendChild(h);const l=h.getContext(\"2d\");if(this.renderWaveform(t,e,l),h.width>0&&h.height>0){const t=h.cloneNode(),i=t.getContext(\"2d\");i.drawImage(h,0,0),i.globalCompositeOperation=\"source-in\",i.fillStyle=this.convertColorValues(e.progressColor),i.fillRect(0,0,h.width,h.height),o.appendChild(t)}}renderMultiCanvas(t,e,i,s,n,r){const o=this.getPixelRatio(),{clientWidth:a}=this.scrollContainer,l=i/o;let d=Math.min(h.MAX_CANVAS_WIDTH,a,l),c={};if(e.barWidth||e.barGap){const t=e.barWidth||.5,i=t+(e.barGap||t/2);d%i!=0&&(d=Math.floor(d/i)*i)}if(0===d)return;const u=i=>{if(i<0||i>=p)return;if(c[i])return;c[i]=!0;const o=i*d;let a=Math.min(l-o,d);if(e.barWidth||e.barGap){const t=e.barWidth||.5,i=t+(e.barGap||t/2);a=Math.floor(a/i)*i}if(a<=0)return;const h=t.map((t=>{const e=Math.floor(o/l*t.length),i=Math.floor((o+a)/l*t.length);return t.slice(e,i)}));this.renderSingleCanvas(h,e,a,s,o,n,r)},p=Math.ceil(l/d);if(!this.isScrollable){for(let t=0;t<p;t++)u(t);return}const m=this.scrollContainer.scrollLeft/l,f=Math.floor(m*p);if(u(f-1),u(f),u(f+1),p>1){const t=this.on(\"scroll\",(()=>{const{scrollLeft:t}=this.scrollContainer,e=Math.floor(t/l*p);Object.keys(c).length>h.MAX_NODES&&(n.innerHTML=\"\",r.innerHTML=\"\",c={}),u(e-1),u(e),u(e+1)}));this.unsubscribeOnScroll.push(t)}}renderChannel(t,e,i,s){var{overlay:n}=e,r=function(t,e){var i={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(null!=t&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(t);n<s.length;n++)e.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(i[s[n]]=t[s[n]])}return i}(e,[\"overlay\"]);const o=document.createElement(\"div\"),a=this.getHeight(r.height,r.splitChannels);o.style.height=`${a}px`,n&&s>0&&(o.style.marginTop=`-${a}px`),this.canvasWrapper.style.minHeight=`${a}px`,this.canvasWrapper.appendChild(o);const h=o.cloneNode();this.progressWrapper.appendChild(h),this.renderMultiCanvas(t,r,i,a,o,h)}render(e){return t(this,void 0,void 0,(function*(){var t;this.timeouts.forEach((t=>t())),this.timeouts=[],this.canvasWrapper.innerHTML=\"\",this.progressWrapper.innerHTML=\"\",null!=this.options.width&&(this.scrollContainer.style.width=\"number\"==typeof this.options.width?`${this.options.width}px`:this.options.width);const i=this.getPixelRatio(),s=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrollable=n>s;const r=this.options.fillParent&&!this.isScrollable,o=(r?s:n)*i;if(this.wrapper.style.width=r?\"100%\":`${n}px`,this.scrollContainer.style.overflowX=this.isScrollable?\"auto\":\"hidden\",this.scrollContainer.classList.toggle(\"noScrollbar\",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.audioData=e,this.emit(\"render\"),this.options.splitChannels)for(let i=0;i<e.numberOfChannels;i++){const s=Object.assign(Object.assign({},this.options),null===(t=this.options.splitChannels)||void 0===t?void 0:t[i]);this.renderChannel([e.getChannelData(i)],s,o,i)}else{const t=[e.getChannelData(0)];e.numberOfChannels>1&&t.push(e.getChannelData(1)),this.renderChannel(t,this.options,o,0)}Promise.resolve().then((()=>this.emit(\"rendered\")))}))}reRender(){if(this.unsubscribeOnScroll.forEach((t=>t())),this.unsubscribeOnScroll=[],!this.audioData)return;const{scrollWidth:t}=this.scrollContainer,{right:e}=this.progressWrapper.getBoundingClientRect();if(this.render(this.audioData),this.isScrollable&&t!==this.scrollContainer.scrollWidth){const{right:t}=this.progressWrapper.getBoundingClientRect();let i=t-e;i*=2,i=i<0?Math.floor(i):Math.ceil(i),i/=2,this.scrollContainer.scrollLeft+=i}}zoom(t){this.options.minPxPerSec=t,this.reRender()}scrollIntoView(t,e=!1){const{scrollLeft:i,scrollWidth:s,clientWidth:n}=this.scrollContainer,r=t*s,o=i,a=i+n,h=n/2;if(this.isDragging){const t=30;r+t>a?this.scrollContainer.scrollLeft+=t:r-t<o&&(this.scrollContainer.scrollLeft-=t)}else{(r<o||r>a)&&(this.scrollContainer.scrollLeft=r-(this.options.autoCenter?h:0));const t=r-i-h;e&&this.options.autoCenter&&t>0&&(this.scrollContainer.scrollLeft+=Math.min(t,10))}{const t=this.scrollContainer.scrollLeft,e=t/s,i=(t+n)/s;this.emit(\"scroll\",e,i,t,t+n)}}renderProgress(t,e){if(isNaN(t))return;const i=100*t;this.canvasWrapper.style.clipPath=`polygon(${i}% 0%, 100% 0%, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.transform=`translateX(-${100===Math.round(i)?this.options.cursorWidth:0}px)`,this.isScrollable&&this.options.autoScroll&&this.scrollIntoView(t,e)}exportImage(e,i,s){return t(this,void 0,void 0,(function*(){const t=this.canvasWrapper.querySelectorAll(\"canvas\");if(!t.length)throw new Error(\"No waveform data\");if(\"dataURL\"===s){const s=Array.from(t).map((t=>t.toDataURL(e,i)));return Promise.resolve(s)}return Promise.all(Array.from(t).map((t=>new Promise(((s,n)=>{t.toBlob((t=>{t?s(t):n(new Error(\"Could not export image\"))}),e,i)})))))}))}}h.MAX_CANVAS_WIDTH=8e3,h.MAX_NODES=10;class l extends e{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on(\"tick\",(()=>{requestAnimationFrame((()=>{this.emit(\"tick\")}))})),this.emit(\"tick\")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}class d extends e{constructor(t=new AudioContext){super(),this.bufferNode=null,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this._playbackRate=1,this._duration=void 0,this.buffer=null,this.currentSrc=\"\",this.paused=!0,this.crossOrigin=null,this.seeking=!1,this.autoplay=!1,this.addEventListener=this.on,this.removeEventListener=this.un,this.audioContext=t,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return t(this,void 0,void 0,(function*(){}))}get src(){return this.currentSrc}set src(t){if(this.currentSrc=t,this._duration=void 0,!t)return this.buffer=null,void this.emit(\"emptied\");fetch(t).then((e=>{if(e.status>=400)throw new Error(`Failed to fetch ${t}: ${e.status} (${e.statusText})`);return e.arrayBuffer()})).then((e=>this.currentSrc!==t?null:this.audioContext.decodeAudioData(e))).then((e=>{this.currentSrc===t&&(this.buffer=e,this.emit(\"loadedmetadata\"),this.emit(\"canplay\"),this.autoplay&&this.play())}))}_play(){var t;if(!this.paused)return;this.paused=!1,null===(t=this.bufferNode)||void 0===t||t.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.buffer&&(this.bufferNode.buffer=this.buffer),this.bufferNode.playbackRate.value=this._playbackRate,this.bufferNode.connect(this.gainNode);let e=this.playedDuration*this._playbackRate;(e>=this.duration||e<0)&&(e=0,this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,e),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit(\"ended\"))}}_pause(){var t;this.paused=!0,null===(t=this.bufferNode)||void 0===t||t.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime}play(){return t(this,void 0,void 0,(function*(){this.paused&&(this._play(),this.emit(\"play\"))}))}pause(){this.paused||(this._pause(),this.emit(\"pause\"))}stopAt(t){const e=t-this.currentTime,i=this.bufferNode;null==i||i.stop(this.audioContext.currentTime+e),null==i||i.addEventListener(\"ended\",(()=>{i===this.bufferNode&&(this.bufferNode=null,this.pause())}),{once:!0})}setSinkId(e){return t(this,void 0,void 0,(function*(){return this.audioContext.setSinkId(e)}))}get playbackRate(){return this._playbackRate}set playbackRate(t){this._playbackRate=t,this.bufferNode&&(this.bufferNode.playbackRate.value=t)}get currentTime(){return(this.paused?this.playedDuration:this.playedDuration+(this.audioContext.currentTime-this.playStartTime))*this._playbackRate}set currentTime(t){const e=!this.paused;e&&this._pause(),this.playedDuration=t/this._playbackRate,e&&this._play(),this.emit(\"seeking\"),this.emit(\"timeupdate\")}get duration(){var t,e;return null!==(t=this._duration)&&void 0!==t?t:(null===(e=this.buffer)||void 0===e?void 0:e.duration)||0}set duration(t){this._duration=t}get volume(){return this.gainNode.gain.value}set volume(t){this.gainNode.gain.value=t,this.emit(\"volumechange\")}get muted(){return this._muted}set muted(t){this._muted!==t&&(this._muted=t,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}canPlayType(t){return/^(audio|video)\\//.test(t)}getGainNode(){return this.gainNode}getChannelData(){const t=[];if(!this.buffer)return t;const e=this.buffer.numberOfChannels;for(let i=0;i<e;i++)t.push(this.buffer.getChannelData(i));return t}}const c={waveColor:\"#999\",progressColor:\"#555\",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class u extends a{static create(t){return new u(t)}constructor(t){const e=t.media||(\"WebAudio\"===t.backend?new d:void 0);super({media:e,mediaControls:t.mediaControls,autoplay:t.autoplay,playbackRate:t.audioRate}),this.plugins=[],this.decodedData=null,this.stopAtPosition=null,this.subscriptions=[],this.mediaSubscriptions=[],this.abortController=null,this.options=Object.assign({},c,t),this.timer=new l;const i=e?void 0:this.getMediaElement();this.renderer=new h(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const s=this.options.url||this.getSrc()||\"\";Promise.resolve().then((()=>{this.emit(\"init\");const{peaks:t,duration:e}=this.options;(s||t&&e)&&this.load(s,t,e).catch((()=>null))}))}updateProgress(t=this.getCurrentTime()){return this.renderer.renderProgress(t/this.getDuration(),this.isPlaying()),t}initTimerEvents(){this.subscriptions.push(this.timer.on(\"tick\",(()=>{if(!this.isSeeking()){const t=this.updateProgress();this.emit(\"timeupdate\",t),this.emit(\"audioprocess\",t),null!=this.stopAtPosition&&this.isPlaying()&&t>=this.stopAtPosition&&this.pause()}})))}initPlayerEvents(){this.isPlaying()&&(this.emit(\"play\"),this.timer.start()),this.mediaSubscriptions.push(this.onMediaEvent(\"timeupdate\",(()=>{const t=this.updateProgress();this.emit(\"timeupdate\",t)})),this.onMediaEvent(\"play\",(()=>{this.emit(\"play\"),this.timer.start()})),this.onMediaEvent(\"pause\",(()=>{this.emit(\"pause\"),this.timer.stop(),this.stopAtPosition=null})),this.onMediaEvent(\"emptied\",(()=>{this.timer.stop(),this.stopAtPosition=null})),this.onMediaEvent(\"ended\",(()=>{this.emit(\"timeupdate\",this.getDuration()),this.emit(\"finish\"),this.stopAtPosition=null})),this.onMediaEvent(\"seeking\",(()=>{this.emit(\"seeking\",this.getCurrentTime())})),this.onMediaEvent(\"error\",(()=>{var t;this.emit(\"error\",null!==(t=this.getMediaElement().error)&&void 0!==t?t:new Error(\"Media error\")),this.stopAtPosition=null})))}initRendererEvents(){this.subscriptions.push(this.renderer.on(\"click\",((t,e)=>{this.options.interact&&(this.seekTo(t),this.emit(\"interaction\",t*this.getDuration()),this.emit(\"click\",t,e))})),this.renderer.on(\"dblclick\",((t,e)=>{this.emit(\"dblclick\",t,e)})),this.renderer.on(\"scroll\",((t,e,i,s)=>{const n=this.getDuration();this.emit(\"scroll\",t*n,e*n,i,s)})),this.renderer.on(\"render\",(()=>{this.emit(\"redraw\")})),this.renderer.on(\"rendered\",(()=>{this.emit(\"redrawcomplete\")})),this.renderer.on(\"dragstart\",(t=>{this.emit(\"dragstart\",t)})),this.renderer.on(\"dragend\",(t=>{this.emit(\"dragend\",t)})));{let t;this.subscriptions.push(this.renderer.on(\"drag\",(e=>{if(!this.options.interact)return;let i;this.renderer.renderProgress(e),clearTimeout(t),this.isPlaying()?i=0:!0===this.options.dragToSeek?i=200:\"object\"==typeof this.options.dragToSeek&&void 0!==this.options.dragToSeek&&(i=this.options.dragToSeek.debounceTime),t=setTimeout((()=>{this.seekTo(e)}),i),this.emit(\"interaction\",e*this.getDuration()),this.emit(\"drag\",e)})))}}initPlugins(){var t;(null===(t=this.options.plugins)||void 0===t?void 0:t.length)&&this.options.plugins.forEach((t=>{this.registerPlugin(t)}))}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach((t=>t())),this.mediaSubscriptions=[]}setOptions(t){this.options=Object.assign({},this.options,t),t.duration&&!t.peaks&&(this.decodedData=i.createBuffer(this.exportPeaks(),t.duration)),t.peaks&&t.duration&&(this.decodedData=i.createBuffer(t.peaks,t.duration)),this.renderer.setOptions(this.options),t.audioRate&&this.setPlaybackRate(t.audioRate),null!=t.mediaControls&&(this.getMediaElement().controls=t.mediaControls)}registerPlugin(t){t._init(this),this.plugins.push(t);const e=t.once(\"destroy\",(()=>{this.plugins=this.plugins.filter((e=>e!==t)),this.subscriptions=this.subscriptions.filter((t=>t!==e))}));return this.subscriptions.push(e),t}getWrapper(){return this.renderer.getWrapper()}getWidth(){return this.renderer.getWidth()}getScroll(){return this.renderer.getScroll()}setScroll(t){return this.renderer.setScroll(t)}setScrollTime(t){const e=t/this.getDuration();this.renderer.setScrollPercentage(e)}getActivePlugins(){return this.plugins}loadAudio(e,s,n,r){return t(this,void 0,void 0,(function*(){var t;if(this.emit(\"load\",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,this.stopAtPosition=null,!s&&!n){const i=this.options.fetchParams||{};window.AbortController&&!i.signal&&(this.abortController=new AbortController,i.signal=null===(t=this.abortController)||void 0===t?void 0:t.signal);const n=t=>this.emit(\"loading\",t);s=yield o.fetchBlob(e,n,i);const r=this.options.blobMimeType;r&&(s=new Blob([s],{type:r}))}\"\"==e?this.getMediaElement().removeAttribute(\"src\"):this.setSrc(e,s);const a=yield new Promise((t=>{const e=r||this.getDuration();e?t(e):this.mediaSubscriptions.push(this.onMediaEvent(\"loadedmetadata\",(()=>t(this.getDuration())),{once:!0}))}));if(!e&&!s){const t=this.getMediaElement();t instanceof d&&(t.duration=a)}if(n)this.decodedData=i.createBuffer(n,a||0);else if(s){const t=yield s.arrayBuffer();this.decodedData=yield i.decode(t,this.options.sampleRate)}this.decodedData&&(this.emit(\"decode\",this.getDuration()),this.renderer.render(this.decodedData)),this.emit(\"ready\",this.getDuration())}))}load(e,i,s){return t(this,void 0,void 0,(function*(){try{return yield this.loadAudio(e,void 0,i,s)}catch(t){throw this.emit(\"error\",t),t}}))}loadBlob(e,i,s){return t(this,void 0,void 0,(function*(){try{return yield this.loadAudio(\"\",e,i,s)}catch(t){throw this.emit(\"error\",t),t}}))}zoom(t){if(!this.decodedData)throw new Error(\"No audio loaded\");this.renderer.zoom(t),this.emit(\"zoom\",t)}getDecodedData(){return this.decodedData}exportPeaks({channels:t=2,maxLength:e=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error(\"The audio has not been decoded yet\");const s=Math.min(t,this.decodedData.numberOfChannels),n=[];for(let t=0;t<s;t++){const s=this.decodedData.getChannelData(t),r=[],o=s.length/e;for(let t=0;t<e;t++){const e=s.slice(Math.floor(t*o),Math.ceil((t+1)*o));let n=0;for(let t=0;t<e.length;t++){const i=e[t];Math.abs(i)>Math.abs(n)&&(n=i)}r.push(Math.round(n*i)/i)}n.push(r)}return n}getDuration(){let t=super.getDuration()||0;return 0!==t&&t!==1/0||!this.decodedData||(t=this.decodedData.duration),t}toggleInteraction(t){this.options.interact=t}setTime(t){this.stopAtPosition=null,super.setTime(t),this.updateProgress(t),this.emit(\"timeupdate\",t)}seekTo(t){const e=this.getDuration()*t;this.setTime(e)}play(e,i){const s=Object.create(null,{play:{get:()=>super.play}});return t(this,void 0,void 0,(function*(){null!=e&&this.setTime(e);const t=yield s.play.call(this);return null!=i&&(this.media instanceof d?this.media.stopAt(i):this.stopAtPosition=i),t}))}playPause(){return t(this,void 0,void 0,(function*(){return this.isPlaying()?this.pause():this.play()}))}stop(){this.pause(),this.setTime(0)}skip(t){this.setTime(this.getCurrentTime()+t)}empty(){this.load(\"\",[[0]],.001)}setMediaElement(t){this.unsubscribePlayerEvents(),super.setMediaElement(t),this.initPlayerEvents()}exportImage(){return t(this,arguments,void 0,(function*(t=\"image/png\",e=1,i=\"dataURL\"){return this.renderer.exportImage(t,e,i)}))}destroy(){var t;this.emit(\"destroy\"),null===(t=this.abortController)||void 0===t||t.abort(),this.plugins.forEach((t=>t.destroy())),this.subscriptions.forEach((t=>t())),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}u.BasePlugin=class extends e{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}},u.dom=r;export{u as default};\n"], "mappings": ";AAAA,SAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAID,OAAIA,KAAE,UAAW,SAASE,IAAEC,IAAE;AAAC,aAASC,GAAEN,IAAE;AAAC,UAAG;AAAC,QAAAO,GAAEJ,GAAE,KAAKH,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,QAAAK,GAAEL,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASQ,GAAER,IAAE;AAAC,UAAG;AAAC,QAAAO,GAAEJ,GAAE,MAAMH,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,QAAAK,GAAEL,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASO,GAAEP,IAAE;AAAC,UAAIC;AAAE,MAAAD,GAAE,OAAKI,GAAEJ,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAaC,KAAED,KAAE,IAAIC,GAAG,SAASF,IAAE;AAAC,QAAAA,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAG,KAAKK,IAAEE,EAAC;AAAA,IAAC;AAAC,IAAAD,IAAGJ,KAAEA,GAAE,MAAMH,IAAEC,MAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAqD,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,GAAGQ,IAAEC,IAAEC,IAAE;AAAC,QAAG,KAAK,UAAUF,EAAC,MAAI,KAAK,UAAUA,EAAC,IAAE,oBAAI,QAAK,KAAK,UAAUA,EAAC,EAAE,IAAIC,EAAC,GAAE,QAAMC,KAAE,SAAOA,GAAE,MAAK;AAAC,YAAMA,KAAE,MAAI;AAAC,aAAK,GAAGF,IAAEE,EAAC,GAAE,KAAK,GAAGF,IAAEC,EAAC;AAAA,MAAC;AAAE,aAAO,KAAK,GAAGD,IAAEE,EAAC,GAAEA;AAAA,IAAC;AAAC,WAAM,MAAI,KAAK,GAAGF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,UAAUF,EAAC,MAAI,WAASE,MAAGA,GAAE,OAAOD,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAEC,IAAE;AAAC,WAAO,KAAK,GAAGD,IAAEC,IAAE,EAAC,MAAK,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,OAAKC,IAAE;AAAC,SAAK,UAAUD,EAAC,KAAG,KAAK,UAAUA,EAAC,EAAE,QAAS,CAAAA,OAAGA,GAAE,GAAGC,EAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAC,QAAO,SAASA,IAAEC,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAMF,KAAE,IAAI,aAAa,EAAC,YAAWE,GAAC,CAAC;AAAE,WAAOF,GAAE,gBAAgBC,EAAC,EAAE,QAAS,MAAID,GAAE,MAAM,CAAE;AAAA,EAAC,CAAE;AAAC,GAAE,cAAa,SAASA,IAAEC,IAAE;AAAC,SAAM,YAAU,OAAOD,GAAE,CAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,SAASA,IAAE;AAAC,UAAMC,KAAED,GAAE,CAAC;AAAE,QAAGC,GAAE,KAAM,CAAAD,OAAGA,KAAE,KAAGA,KAAE,EAAG,GAAE;AAAC,YAAME,KAAED,GAAE;AAAO,UAAIE,KAAE;AAAE,eAAQH,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,cAAME,KAAE,KAAK,IAAID,GAAED,EAAC,CAAC;AAAE,QAAAE,KAAEC,OAAIA,KAAED;AAAA,MAAE;AAAC,iBAAUD,MAAKD,GAAE,UAAQA,KAAE,GAAEA,KAAEE,IAAEF,KAAI,CAAAC,GAAED,EAAC,KAAGG;AAAA,IAAC;AAAA,EAAC,EAAEH,EAAC,GAAE,EAAC,UAASC,IAAE,QAAOD,GAAE,CAAC,EAAE,QAAO,YAAWA,GAAE,CAAC,EAAE,SAAOC,IAAE,kBAAiBD,GAAE,QAAO,gBAAe,CAAAC,OAAG,QAAMD,KAAE,SAAOA,GAAEC,EAAC,GAAE,iBAAgB,YAAY,UAAU,iBAAgB,eAAc,YAAY,UAAU,cAAa;AAAC,EAAC;AAAE,SAAS,EAAED,IAAEC,IAAE;AAAC,QAAMC,KAAED,GAAE,QAAM,SAAS,gBAAgBA,GAAE,OAAMD,EAAC,IAAE,SAAS,cAAcA,EAAC;AAAE,aAAS,CAACA,IAAEI,EAAC,KAAI,OAAO,QAAQH,EAAC,EAAE,KAAG,eAAaD,MAAGI,GAAE,YAAS,CAACJ,IAAEC,EAAC,KAAI,OAAO,QAAQG,EAAC,EAAE,CAAAH,cAAa,OAAKC,GAAE,YAAYD,EAAC,IAAE,YAAU,OAAOA,KAAEC,GAAE,YAAY,SAAS,eAAeD,EAAC,CAAC,IAAEC,GAAE,YAAY,EAAEF,IAAEC,EAAC,CAAC;AAAA,MAAM,aAAUD,KAAE,OAAO,OAAOE,GAAE,OAAME,EAAC,IAAE,kBAAgBJ,KAAEE,GAAE,cAAYE,KAAEF,GAAE,aAAaF,IAAEI,GAAE,SAAS,CAAC;AAAE,SAAOF;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,QAAME,KAAE,EAAEJ,IAAEC,MAAG,CAAC,CAAC;AAAE,SAAO,QAAMC,MAAGA,GAAE,YAAYE,EAAC,GAAEA;AAAC;AAAC,IAAI,IAAE,OAAO,OAAO,EAAC,WAAU,MAAK,eAAc,GAAE,SAAQ,EAAC,CAAC;AAAE,IAAM,IAAE,EAAC,WAAU,SAASH,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAMC,KAAE,MAAM,MAAMH,IAAEE,EAAC;AAAE,QAAGC,GAAE,UAAQ,IAAI,OAAM,IAAI,MAAM,mBAAmBH,EAAC,KAAKG,GAAE,MAAM,KAAKA,GAAE,UAAU,GAAG;AAAE,WAAO,SAASH,IAAEC,IAAE;AAAC,QAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,YAAG,CAACD,GAAE,QAAM,CAACA,GAAE,QAAQ;AAAO,cAAME,KAAEF,GAAE,KAAK,UAAU,GAAEG,KAAE,OAAOH,GAAE,QAAQ,IAAI,gBAAgB,CAAC,KAAG;AAAE,YAAII,KAAE;AAAE,cAAMC,KAAE,CAAAL,OAAG,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAAI,OAAI,QAAMJ,KAAE,SAAOA,GAAE,WAAS;AAAE,gBAAMD,KAAE,KAAK,MAAMK,KAAED,KAAE,GAAG;AAAE,UAAAF,GAAEF,EAAC;AAAA,QAAC,CAAE,GAAEO,KAAE,MAAI,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,cAAIP;AAAE,cAAG;AAAC,YAAAA,KAAE,MAAMG,GAAE,KAAK;AAAA,UAAC,SAAOH,IAAE;AAAC;AAAA,UAAM;AAAC,UAAAA,GAAE,SAAOM,GAAEN,GAAE,KAAK,GAAE,MAAMO,GAAE;AAAA,QAAE,CAAE;AAAE,QAAAA,GAAE;AAAA,MAAC,CAAE;AAAA,IAAC,EAAEH,GAAE,MAAM,GAAEF,EAAC,GAAEE,GAAE,KAAK;AAAA,EAAC,CAAE;AAAC,EAAC;AAAE,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAM,GAAE,KAAK,kBAAgB,OAAGA,GAAE,SAAO,KAAK,QAAMA,GAAE,OAAM,KAAK,kBAAgB,QAAI,KAAK,QAAM,SAAS,cAAc,OAAO,GAAEA,GAAE,kBAAgB,KAAK,MAAM,WAAS,OAAIA,GAAE,aAAW,KAAK,MAAM,WAAS,OAAI,QAAMA,GAAE,gBAAc,KAAK,aAAa,WAAW,MAAI;AAAC,cAAMA,GAAE,iBAAe,KAAK,MAAM,eAAaA,GAAE;AAAA,IAAa,GAAG,EAAC,MAAK,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,MAAM,iBAAiBF,IAAEC,IAAEC,EAAC,GAAE,MAAI,KAAK,MAAM,oBAAoBF,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,MAAM,cAAY,KAAK,MAAM,OAAK;AAAA,EAAE;AAAA,EAAC,YAAW;AAAC,UAAMF,KAAE,KAAK,OAAO;AAAE,IAAAA,GAAE,WAAW,OAAO,KAAG,IAAI,gBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAM,OAAK,KAAK,MAAM,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO;AAAE,QAAGF,MAAGE,OAAIF,GAAE;AAAO,SAAK,UAAU;AAAE,UAAMG,KAAEF,cAAa,SAAO,KAAK,YAAYA,GAAE,IAAI,KAAG,CAACD,MAAG,IAAI,gBAAgBC,EAAC,IAAED;AAAE,IAAAE,MAAG,KAAK,MAAM,gBAAgB,KAAK;AAAE,QAAG;AAAC,WAAK,MAAM,MAAIC;AAAA,IAAC,SAAOF,IAAE;AAAC,WAAK,MAAM,MAAID;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,oBAAkB,KAAK,MAAM,MAAM,GAAE,KAAK,MAAM,OAAO,GAAE,KAAK,UAAU,GAAE,KAAK,MAAM,gBAAgB,KAAK,GAAE,KAAK,MAAM,KAAK;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAE;AAAC,SAAK,QAAMA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,aAAO,KAAK,MAAM,KAAK;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAM,CAAC,KAAK,MAAM,UAAQ,CAAC,KAAK,MAAM;AAAA,EAAK;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,MAAM,cAAY,KAAK,IAAI,GAAE,KAAK,IAAIA,IAAE,KAAK,YAAY,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,MAAM;AAAA,EAAQ;AAAA,EAAC,iBAAgB;AAAC,WAAO,KAAK,MAAM;AAAA,EAAW;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,MAAM;AAAA,EAAM;AAAA,EAAC,UAAUA,IAAE;AAAC,SAAK,MAAM,SAAOA;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK,MAAM;AAAA,EAAK;AAAA,EAAC,SAASA,IAAE;AAAC,SAAK,MAAM,QAAMA;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,MAAM;AAAA,EAAY;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,MAAM;AAAA,EAAO;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,YAAMA,OAAI,KAAK,MAAM,iBAAeA,KAAG,KAAK,MAAM,eAAaD;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAO,KAAK,MAAM,UAAUA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,eAAa,OAAG,KAAK,YAAU,MAAK,KAAK,iBAAe,MAAK,KAAK,qBAAmB,GAAE,KAAK,aAAW,OAAG,KAAK,gBAAc,CAAC,GAAE,KAAK,sBAAoB,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,UAAQD;AAAE,UAAME,KAAE,KAAK,2BAA2BF,GAAE,SAAS;AAAE,SAAK,SAAOE;AAAE,UAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,SAAS;AAAE,IAAAF,GAAE,YAAYC,EAAC,GAAE,KAAK,YAAUA,IAAE,KAAK,kBAAgBC,GAAE,cAAc,SAAS,GAAE,KAAK,UAAQA,GAAE,cAAc,UAAU,GAAE,KAAK,gBAAcA,GAAE,cAAc,WAAW,GAAE,KAAK,kBAAgBA,GAAE,cAAc,WAAW,GAAE,KAAK,SAAOA,GAAE,cAAc,SAAS,GAAEH,MAAGG,GAAE,YAAYH,EAAC,GAAE,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAE;AAAC,QAAIC;AAAE,QAAG,YAAU,OAAOD,KAAEC,KAAE,SAAS,cAAcD,EAAC,IAAEA,cAAa,gBAAcC,KAAED,KAAG,CAACC,GAAE,OAAM,IAAI,MAAM,qBAAqB;AAAE,WAAOA;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAMD,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,QAAQ,sBAAsB,GAAEC,KAAEF,GAAE,UAAQC,GAAE,MAAKE,KAAEH,GAAE,UAAQC,GAAE;AAAI,aAAM,CAACC,KAAED,GAAE,OAAME,KAAEF,GAAE,MAAM;AAAA,IAAC;AAAE,QAAG,KAAK,QAAQ,iBAAiB,SAAS,CAAAA,OAAG;AAAC,YAAK,CAACC,IAAEC,EAAC,IAAEH,GAAEC,EAAC;AAAE,WAAK,KAAK,SAAQC,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,QAAQ,iBAAiB,YAAY,CAAAF,OAAG;AAAC,YAAK,CAACC,IAAEC,EAAC,IAAEH,GAAEC,EAAC;AAAE,WAAK,KAAK,YAAWC,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,SAAK,KAAK,QAAQ,cAAY,YAAU,OAAO,KAAK,QAAQ,cAAY,KAAK,SAAS,GAAE,KAAK,gBAAgB,iBAAiB,UAAU,MAAI;AAAC,YAAK,EAAC,YAAWH,IAAE,aAAYC,IAAE,aAAYC,GAAC,IAAE,KAAK,iBAAgBC,KAAEH,KAAEC,IAAEG,MAAGJ,KAAEE,MAAGD;AAAE,WAAK,KAAK,UAASE,IAAEC,IAAEJ,IAAEA,KAAEE,EAAC;AAAA,IAAC,CAAE,GAAE,cAAY,OAAO,gBAAe;AAAC,YAAMF,KAAE,KAAK,YAAY,GAAG;AAAE,WAAK,iBAAe,IAAI,eAAgB,MAAI;AAAC,QAAAA,GAAE,EAAE,KAAM,MAAI,KAAK,kBAAkB,CAAE,EAAE,MAAO,MAAI;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE,GAAE,KAAK,eAAe,QAAQ,KAAK,eAAe;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,UAAMA,KAAE,KAAK,OAAO;AAAY,IAAAA,OAAI,KAAK,sBAAoB,WAAS,KAAK,QAAQ,WAAS,KAAK,qBAAmBA,IAAE,KAAK,SAAS;AAAA,EAAE;AAAA,EAAC,WAAU;AAAC,SAAK,cAAc,KAAK,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,KAAI;AAAC,UAAG,CAACN,GAAE,QAAM,MAAI;AAAA,MAAC;AAAE,YAAMO,KAAE,WAAW,mBAAmB,EAAE;AAAQ,UAAIC,KAAE,MAAI;AAAA,MAAC;AAAE,YAAMC,KAAE,CAAAA,OAAG;AAAC,YAAGA,GAAE,WAASJ,GAAE;AAAO,QAAAI,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE,YAAIC,KAAED,GAAE,SAAQE,KAAEF,GAAE,SAAQG,KAAE;AAAG,cAAM,IAAE,KAAK,IAAI,GAAE,IAAE,CAAAT,OAAG;AAAC,cAAGA,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEI,MAAG,KAAK,IAAI,IAAE,IAAED,GAAE;AAAO,gBAAMD,KAAEF,GAAE,SAAQK,KAAEL,GAAE,SAAQM,KAAEJ,KAAEK,IAAEG,KAAEL,KAAEG;AAAE,cAAGC,MAAG,KAAK,IAAIH,EAAC,IAAEL,MAAG,KAAK,IAAIS,EAAC,IAAET,IAAE;AAAC,kBAAMD,KAAEH,GAAE,sBAAsB,GAAE,EAAC,MAAKI,IAAE,KAAIE,GAAC,IAAEH;AAAE,YAAAS,OAAI,QAAMV,MAAGA,GAAEQ,KAAEN,IAAEO,KAAEL,EAAC,GAAEM,KAAE,OAAIX,GAAEQ,IAAEI,IAAER,KAAED,IAAEI,KAAEF,EAAC,GAAEI,KAAEL,IAAEM,KAAEH;AAAA,UAAC;AAAA,QAAC,GAAE,IAAE,CAAAP,OAAG;AAAC,cAAGW,IAAE;AAAC,kBAAMV,KAAED,GAAE,SAAQG,KAAEH,GAAE,SAAQI,KAAEL,GAAE,sBAAsB,GAAE,EAAC,MAAKM,IAAE,KAAIC,GAAC,IAAEF;AAAE,oBAAMF,MAAGA,GAAED,KAAEI,IAAEF,KAAEG,EAAC;AAAA,UAAC;AAAC,UAAAC,GAAE;AAAA,QAAC,GAAE,IAAE,CAAAR,OAAG;AAAC,UAAAA,GAAE,iBAAeA,GAAE,kBAAgB,SAAS,mBAAiB,EAAEA,EAAC;AAAA,QAAC,GAAE,IAAE,CAAAA,OAAG;AAAC,UAAAY,OAAIZ,GAAE,gBAAgB,GAAEA,GAAE,eAAe;AAAA,QAAE,GAAE,IAAE,CAAAA,OAAG;AAAC,UAAAY,MAAGZ,GAAE,eAAe;AAAA,QAAC;AAAE,iBAAS,iBAAiB,eAAc,CAAC,GAAE,SAAS,iBAAiB,aAAY,CAAC,GAAE,SAAS,iBAAiB,cAAa,CAAC,GAAE,SAAS,iBAAiB,iBAAgB,CAAC,GAAE,SAAS,iBAAiB,aAAY,GAAE,EAAC,SAAQ,MAAE,CAAC,GAAE,SAAS,iBAAiB,SAAQ,GAAE,EAAC,SAAQ,KAAE,CAAC,GAAEQ,KAAE,MAAI;AAAC,mBAAS,oBAAoB,eAAc,CAAC,GAAE,SAAS,oBAAoB,aAAY,CAAC,GAAE,SAAS,oBAAoB,cAAa,CAAC,GAAE,SAAS,oBAAoB,iBAAgB,CAAC,GAAE,SAAS,oBAAoB,aAAY,CAAC,GAAE,WAAY,MAAI;AAAC,qBAAS,oBAAoB,SAAQ,GAAE,EAAC,SAAQ,KAAE,CAAC;AAAA,UAAC,GAAG,EAAE;AAAA,QAAC;AAAA,MAAC;AAAE,aAAOR,GAAE,iBAAiB,eAAcS,EAAC,GAAE,MAAI;AAAC,QAAAD,GAAE,GAAER,GAAE,oBAAoB,eAAcS,EAAC;AAAA,MAAC;AAAA,IAAC,EAAE,KAAK,SAAS,CAACT,IAAEC,IAAEC,OAAI;AAAC,WAAK,KAAK,QAAO,KAAK,IAAI,GAAE,KAAK,IAAI,GAAEA,KAAE,KAAK,QAAQ,sBAAsB,EAAE,KAAK,CAAC,CAAC;AAAA,IAAC,GAAI,CAAAF,OAAG;AAAC,WAAK,aAAW,MAAG,KAAK,KAAK,aAAY,KAAK,IAAI,GAAE,KAAK,IAAI,GAAEA,KAAE,KAAK,QAAQ,sBAAsB,EAAE,KAAK,CAAC,CAAC;AAAA,IAAC,GAAI,CAAAA,OAAG;AAAC,WAAK,aAAW,OAAG,KAAK,KAAK,WAAU,KAAK,IAAI,GAAE,KAAK,IAAI,GAAEA,KAAE,KAAK,QAAQ,sBAAsB,EAAE,KAAK,CAAC,CAAC;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEC,IAAE;AAAC,QAAIC;AAAE,UAAMC,MAAG,UAAQD,KAAE,KAAK,cAAY,WAASA,KAAE,SAAOA,GAAE,qBAAmB;AAAE,QAAG,QAAMF,GAAE,QAAO;AAAI,QAAG,CAAC,MAAM,OAAOA,EAAC,CAAC,EAAE,QAAO,OAAOA,EAAC;AAAE,QAAG,WAASA,IAAE;AAAC,YAAMA,KAAE,KAAK,OAAO,gBAAc;AAAI,cAAO,QAAMC,KAAE,SAAOA,GAAE,MAAO,CAAAD,OAAG,CAACA,GAAE,OAAQ,KAAGA,KAAEG,KAAEH;AAAA,IAAC;AAAC,WAAO;AAAA,EAAG;AAAA,EAAC,WAAU;AAAC,UAAMA,KAAE,SAAS,cAAc,KAAK,GAAEC,KAAED,GAAE,aAAa,EAAC,MAAK,OAAM,CAAC,GAAEE,KAAE,KAAK,QAAQ,YAAU,YAAU,OAAO,KAAK,QAAQ,WAAS,KAAK,QAAQ,SAAS,QAAQ,MAAK,EAAE,IAAE;AAAG,WAAOD,GAAE,YAAU;AAAA,cAAiBC,KAAE,WAAWA,EAAC,MAAI,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAguB,KAAK,UAAU,KAAK,QAAQ,QAAO,KAAK,QAAQ,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAyjC,CAACF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWD,IAAE;AAAC,QAAG,KAAK,QAAQ,cAAYA,GAAE,WAAU;AAAC,YAAMC,KAAE,KAAK,2BAA2BD,GAAE,SAAS;AAAE,MAAAC,GAAE,YAAY,KAAK,SAAS,GAAE,KAAK,SAAOA;AAAA,IAAC;AAAC,aAAKD,GAAE,cAAY,YAAU,OAAO,KAAK,QAAQ,cAAY,KAAK,SAAS,GAAE,KAAK,UAAQA,IAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAW;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAU;AAAA,EAAC,UAAUA,IAAE;AAAC,SAAK,gBAAgB,aAAWA;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAK,EAAC,aAAYC,GAAC,IAAE,KAAK,iBAAgBC,KAAED,KAAED;AAAE,SAAK,UAAUE,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAIF,IAAEC;AAAE,SAAK,cAAc,QAAS,CAAAD,OAAGA,GAAE,CAAE,GAAE,KAAK,UAAU,OAAO,GAAE,UAAQA,KAAE,KAAK,mBAAiB,WAASA,MAAGA,GAAE,WAAW,GAAE,UAAQC,KAAE,KAAK,wBAAsB,WAASA,MAAGA,GAAE,QAAS,CAAAD,OAAGA,GAAE,CAAE,GAAE,KAAK,sBAAoB,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAE,IAAG;AAAC,QAAIC,IAAEC;AAAE,UAAMC,KAAE,MAAI;AAAC,MAAAF,MAAG,aAAaA,EAAC,GAAEC,MAAGA,GAAE;AAAA,IAAC;AAAE,WAAO,KAAK,SAAS,KAAKC,EAAC,GAAE,MAAI,IAAI,QAAS,CAACC,IAAEC,OAAI;AAAC,MAAAF,GAAE,GAAED,KAAEG,IAAEJ,KAAE,WAAY,MAAI;AAAC,QAAAA,KAAE,QAAOC,KAAE,QAAOE,GAAE;AAAA,MAAC,GAAGJ,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,QAAG,CAAC,MAAM,QAAQA,EAAC,EAAE,QAAOA,MAAG;AAAG,QAAGA,GAAE,SAAO,EAAE,QAAOA,GAAE,CAAC,KAAG;AAAG,UAAMC,KAAE,SAAS,cAAc,QAAQ,GAAEC,KAAED,GAAE,WAAW,IAAI,GAAEE,KAAEF,GAAE,UAAQ,OAAO,oBAAkB,IAAGG,KAAEF,GAAE,qBAAqB,GAAE,GAAE,GAAEC,EAAC,GAAEE,KAAE,KAAGL,GAAE,SAAO;AAAG,WAAOA,GAAE,QAAS,CAACA,IAAEC,OAAI;AAAC,YAAMC,KAAED,KAAEI;AAAE,MAAAD,GAAE,aAAaF,IAAEF,EAAC;AAAA,IAAC,CAAE,GAAEI;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,IAAI,GAAE,OAAO,oBAAkB,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,KAAGA,GAAE,CAAC,GAAEM,KAAEF,GAAE,QAAO,EAAC,OAAMG,IAAE,QAAOC,GAAC,IAAEN,GAAE,QAAOO,KAAED,KAAE,GAAEE,KAAE,KAAK,cAAc,GAAEC,KAAEV,GAAE,WAASA,GAAE,WAASS,KAAE,GAAEE,KAAEX,GAAE,SAAOA,GAAE,SAAOS,KAAET,GAAE,WAASU,KAAE,IAAE,GAAE,IAAEV,GAAE,aAAW,GAAE,IAAEM,MAAGI,KAAEC,MAAGN,IAAE,IAAE,KAAG,eAAcJ,KAAE,cAAY;AAAO,IAAAA,GAAE,UAAU;AAAE,QAAI,IAAE,GAAE,IAAE,GAAE,IAAE;AAAE,aAAQF,KAAE,GAAEA,MAAGM,IAAEN,MAAI;AAAC,YAAMM,KAAE,KAAK,MAAMN,KAAE,CAAC;AAAE,UAAGM,KAAE,GAAE;AAAC,cAAMN,KAAE,KAAK,MAAM,IAAES,KAAEN,EAAC,GAAEC,KAAEJ,KAAE,KAAK,MAAM,IAAES,KAAEN,EAAC,KAAG;AAAE,YAAIE,KAAEI,KAAET;AAAE,kBAAQC,GAAE,WAASI,KAAE,IAAE,aAAWJ,GAAE,aAAWI,KAAEG,KAAEJ,KAAGF,GAAE,CAAC,EAAE,KAAGS,KAAEC,KAAGP,IAAEM,IAAEP,IAAE,CAAC,GAAE,IAAEE,IAAE,IAAE,GAAE,IAAE;AAAA,MAAC;AAAC,YAAMC,KAAE,KAAK,IAAIH,GAAEJ,EAAC,KAAG,CAAC,GAAEU,KAAE,KAAK,IAAIL,GAAEL,EAAC,KAAG,CAAC;AAAE,MAAAO,KAAE,MAAI,IAAEA,KAAGG,KAAE,MAAI,IAAEA;AAAA,IAAE;AAAC,IAAAR,GAAE,KAAK,GAAEA,GAAE,UAAU;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAAH,OAAG;AAAC,YAAMG,KAAEJ,GAAEC,EAAC,KAAGD,GAAE,CAAC,GAAEK,KAAED,GAAE,QAAO,EAAC,QAAOE,GAAC,IAAEJ,GAAE,QAAOK,KAAED,KAAE,GAAEE,KAAEN,GAAE,OAAO,QAAMG;AAAE,MAAAH,GAAE,OAAO,GAAEK,EAAC;AAAE,UAAIE,KAAE,GAAEC,KAAE;AAAE,eAAQV,KAAE,GAAEA,MAAGK,IAAEL,MAAI;AAAC,cAAMK,KAAE,KAAK,MAAML,KAAEQ,EAAC;AAAE,YAAGH,KAAEI,IAAE;AAAC,gBAAMT,KAAEO,MAAG,KAAK,MAAMG,KAAEH,KAAEJ,EAAC,KAAG,MAAI,MAAIF,KAAE,KAAG;AAAG,UAAAC,GAAE,OAAOO,IAAET,EAAC,GAAES,KAAEJ,IAAEK,KAAE;AAAA,QAAC;AAAC,cAAMJ,KAAE,KAAK,IAAIF,GAAEJ,EAAC,KAAG,CAAC;AAAE,QAAAM,KAAEI,OAAIA,KAAEJ;AAAA,MAAE;AAAC,MAAAJ,GAAE,OAAOO,IAAEF,EAAC;AAAA,IAAC;AAAE,IAAAL,GAAE,UAAU,GAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,GAAE,KAAK,GAAEA,GAAE,UAAU;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEC,IAAEC,IAAE;AAAC,QAAGA,GAAE,YAAU,KAAK,mBAAmBD,GAAE,SAAS,GAAEA,GAAE,eAAe,QAAO,KAAKA,GAAE,eAAeD,IAAEE,EAAC;AAAE,QAAIC,KAAEF,GAAE,aAAW;AAAE,QAAGA,GAAE,WAAU;AAAC,YAAMA,KAAE,MAAM,KAAKD,GAAE,CAAC,CAAC,EAAE,OAAQ,CAACA,IAAEC,OAAI,KAAK,IAAID,IAAE,KAAK,IAAIC,EAAC,CAAC,GAAG,CAAC;AAAE,MAAAE,KAAEF,KAAE,IAAEA,KAAE;AAAA,IAAC;AAAC,IAAAA,GAAE,YAAUA,GAAE,UAAQA,GAAE,WAAS,KAAK,kBAAkBD,IAAEC,IAAEC,IAAEC,EAAC,IAAE,KAAK,mBAAmBH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAc,GAAEC,KAAE,SAAS,cAAc,QAAQ;AAAE,IAAAA,GAAE,QAAM,KAAK,MAAMN,KAAEK,EAAC,GAAEC,GAAE,SAAO,KAAK,MAAML,KAAEI,EAAC,GAAEC,GAAE,MAAM,QAAM,GAAGN,EAAC,MAAKM,GAAE,MAAM,SAAO,GAAGL,EAAC,MAAKK,GAAE,MAAM,OAAK,GAAG,KAAK,MAAMJ,EAAC,CAAC,MAAKC,GAAE,YAAYG,EAAC;AAAE,UAAMC,KAAED,GAAE,WAAW,IAAI;AAAE,QAAG,KAAK,eAAeR,IAAEC,IAAEQ,EAAC,GAAED,GAAE,QAAM,KAAGA,GAAE,SAAO,GAAE;AAAC,YAAMR,KAAEQ,GAAE,UAAU,GAAEN,KAAEF,GAAE,WAAW,IAAI;AAAE,MAAAE,GAAE,UAAUM,IAAE,GAAE,CAAC,GAAEN,GAAE,2BAAyB,aAAYA,GAAE,YAAU,KAAK,mBAAmBD,GAAE,aAAa,GAAEC,GAAE,SAAS,GAAE,GAAEM,GAAE,OAAMA,GAAE,MAAM,GAAEF,GAAE,YAAYN,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAc,GAAE,EAAC,aAAYC,GAAC,IAAE,KAAK,iBAAgBE,KAAEP,KAAEI;AAAE,QAAII,KAAE,KAAK,IAAI,GAAE,kBAAiBH,IAAEE,EAAC,GAAEE,KAAE,CAAC;AAAE,QAAGV,GAAE,YAAUA,GAAE,QAAO;AAAC,YAAMD,KAAEC,GAAE,YAAU,KAAGC,KAAEF,MAAGC,GAAE,UAAQD,KAAE;AAAG,MAAAU,KAAER,MAAG,MAAIQ,KAAE,KAAK,MAAMA,KAAER,EAAC,IAAEA;AAAA,IAAE;AAAC,QAAG,MAAIQ,GAAE;AAAO,UAAME,KAAE,CAAAV,OAAG;AAAC,UAAGA,KAAE,KAAGA,MAAG,EAAE;AAAO,UAAGS,GAAET,EAAC,EAAE;AAAO,MAAAS,GAAET,EAAC,IAAE;AAAG,YAAMI,KAAEJ,KAAEQ;AAAE,UAAIH,KAAE,KAAK,IAAIE,KAAEH,IAAEI,EAAC;AAAE,UAAGT,GAAE,YAAUA,GAAE,QAAO;AAAC,cAAMD,KAAEC,GAAE,YAAU,KAAGC,KAAEF,MAAGC,GAAE,UAAQD,KAAE;AAAG,QAAAO,KAAE,KAAK,MAAMA,KAAEL,EAAC,IAAEA;AAAA,MAAC;AAAC,UAAGK,MAAG,EAAE;AAAO,YAAMC,KAAER,GAAE,IAAK,CAAAA,OAAG;AAAC,cAAMC,KAAE,KAAK,MAAMK,KAAEG,KAAET,GAAE,MAAM,GAAEE,KAAE,KAAK,OAAOI,KAAEC,MAAGE,KAAET,GAAE,MAAM;AAAE,eAAOA,GAAE,MAAMC,IAAEC,EAAC;AAAA,MAAC,CAAE;AAAE,WAAK,mBAAmBM,IAAEP,IAAEM,IAAEJ,IAAEG,IAAEF,IAAEC,EAAC;AAAA,IAAC,GAAE,IAAE,KAAK,KAAKI,KAAEC,EAAC;AAAE,QAAG,CAAC,KAAK,cAAa;AAAC,eAAQV,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAY,GAAEZ,EAAC;AAAE;AAAA,IAAM;AAAC,UAAM,IAAE,KAAK,gBAAgB,aAAWS,IAAE,IAAE,KAAK,MAAM,IAAE,CAAC;AAAE,QAAGG,GAAE,IAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE;AAAC,YAAMZ,KAAE,KAAK,GAAG,UAAU,MAAI;AAAC,cAAK,EAAC,YAAWA,GAAC,IAAE,KAAK,iBAAgBC,KAAE,KAAK,MAAMD,KAAES,KAAE,CAAC;AAAE,eAAO,KAAKE,EAAC,EAAE,SAAO,GAAE,cAAYP,GAAE,YAAU,IAAGC,GAAE,YAAU,IAAGM,KAAE,CAAC,IAAGC,GAAEX,KAAE,CAAC,GAAEW,GAAEX,EAAC,GAAEW,GAAEX,KAAE,CAAC;AAAA,MAAC,CAAE;AAAE,WAAK,oBAAoB,KAAKD,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAG,EAAC,SAAQC,GAAC,IAAEH,IAAEI,KAAE,SAASL,IAAEC,IAAE;AAAC,UAAIC,KAAE,CAAC;AAAE,eAAQC,MAAKH,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEG,EAAC,KAAGF,GAAE,QAAQE,EAAC,IAAE,MAAID,GAAEC,EAAC,IAAEH,GAAEG,EAAC;AAAG,UAAG,QAAMH,MAAG,cAAY,OAAO,OAAO,uBAAsB;AAAC,YAAII,KAAE;AAAE,aAAID,KAAE,OAAO,sBAAsBH,EAAC,GAAEI,KAAED,GAAE,QAAOC,KAAI,CAAAH,GAAE,QAAQE,GAAEC,EAAC,CAAC,IAAE,KAAG,OAAO,UAAU,qBAAqB,KAAKJ,IAAEG,GAAEC,EAAC,CAAC,MAAIF,GAAEC,GAAEC,EAAC,CAAC,IAAEJ,GAAEG,GAAEC,EAAC,CAAC;AAAA,MAAE;AAAC,aAAOF;AAAA,IAAC,EAAED,IAAE,CAAC,SAAS,CAAC;AAAE,UAAMK,KAAE,SAAS,cAAc,KAAK,GAAEC,KAAE,KAAK,UAAUF,GAAE,QAAOA,GAAE,aAAa;AAAE,IAAAC,GAAE,MAAM,SAAO,GAAGC,EAAC,MAAKH,MAAGD,KAAE,MAAIG,GAAE,MAAM,YAAU,IAAIC,EAAC,OAAM,KAAK,cAAc,MAAM,YAAU,GAAGA,EAAC,MAAK,KAAK,cAAc,YAAYD,EAAC;AAAE,UAAME,KAAEF,GAAE,UAAU;AAAE,SAAK,gBAAgB,YAAYE,EAAC,GAAE,KAAK,kBAAkBR,IAAEK,IAAEH,IAAEK,IAAED,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOP,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAID;AAAE,WAAK,SAAS,QAAS,CAAAA,OAAGA,GAAE,CAAE,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,cAAc,YAAU,IAAG,KAAK,gBAAgB,YAAU,IAAG,QAAM,KAAK,QAAQ,UAAQ,KAAK,gBAAgB,MAAM,QAAM,YAAU,OAAO,KAAK,QAAQ,QAAM,GAAG,KAAK,QAAQ,KAAK,OAAK,KAAK,QAAQ;AAAO,YAAME,KAAE,KAAK,cAAc,GAAEC,KAAE,KAAK,gBAAgB,aAAYC,KAAE,KAAK,KAAKH,GAAE,YAAU,KAAK,QAAQ,eAAa,EAAE;AAAE,WAAK,eAAaG,KAAED;AAAE,YAAME,KAAE,KAAK,QAAQ,cAAY,CAAC,KAAK,cAAaC,MAAGD,KAAEF,KAAEC,MAAGF;AAAE,UAAG,KAAK,QAAQ,MAAM,QAAMG,KAAE,SAAO,GAAGD,EAAC,MAAK,KAAK,gBAAgB,MAAM,YAAU,KAAK,eAAa,SAAO,UAAS,KAAK,gBAAgB,UAAU,OAAO,eAAc,CAAC,CAAC,KAAK,QAAQ,aAAa,GAAE,KAAK,OAAO,MAAM,kBAAgB,GAAG,KAAK,QAAQ,eAAa,KAAK,QAAQ,aAAa,IAAG,KAAK,OAAO,MAAM,QAAM,GAAG,KAAK,QAAQ,WAAW,MAAK,KAAK,YAAUH,IAAE,KAAK,KAAK,QAAQ,GAAE,KAAK,QAAQ,cAAc,UAAQC,KAAE,GAAEA,KAAED,GAAE,kBAAiBC,MAAI;AAAC,cAAMC,KAAE,OAAO,OAAO,OAAO,OAAO,CAAC,GAAE,KAAK,OAAO,GAAE,UAAQH,KAAE,KAAK,QAAQ,kBAAgB,WAASA,KAAE,SAAOA,GAAEE,EAAC,CAAC;AAAE,aAAK,cAAc,CAACD,GAAE,eAAeC,EAAC,CAAC,GAAEC,IAAEG,IAAEJ,EAAC;AAAA,MAAC;AAAA,WAAK;AAAC,cAAMF,KAAE,CAACC,GAAE,eAAe,CAAC,CAAC;AAAE,QAAAA,GAAE,mBAAiB,KAAGD,GAAE,KAAKC,GAAE,eAAe,CAAC,CAAC,GAAE,KAAK,cAAcD,IAAE,KAAK,SAAQM,IAAE,CAAC;AAAA,MAAC;AAAC,cAAQ,QAAQ,EAAE,KAAM,MAAI,KAAK,KAAK,UAAU,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,QAAG,KAAK,oBAAoB,QAAS,CAAAN,OAAGA,GAAE,CAAE,GAAE,KAAK,sBAAoB,CAAC,GAAE,CAAC,KAAK,UAAU;AAAO,UAAK,EAAC,aAAYA,GAAC,IAAE,KAAK,iBAAgB,EAAC,OAAMC,GAAC,IAAE,KAAK,gBAAgB,sBAAsB;AAAE,QAAG,KAAK,OAAO,KAAK,SAAS,GAAE,KAAK,gBAAcD,OAAI,KAAK,gBAAgB,aAAY;AAAC,YAAK,EAAC,OAAMA,GAAC,IAAE,KAAK,gBAAgB,sBAAsB;AAAE,UAAIE,KAAEF,KAAEC;AAAE,MAAAC,MAAG,GAAEA,KAAEA,KAAE,IAAE,KAAK,MAAMA,EAAC,IAAE,KAAK,KAAKA,EAAC,GAAEA,MAAG,GAAE,KAAK,gBAAgB,cAAYA;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAE;AAAC,SAAK,QAAQ,cAAYA,IAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,KAAE,OAAG;AAAC,UAAK,EAAC,YAAWC,IAAE,aAAYC,IAAE,aAAYC,GAAC,IAAE,KAAK,iBAAgBC,KAAEL,KAAEG,IAAEG,KAAEJ,IAAEK,KAAEL,KAAEE,IAAEI,KAAEJ,KAAE;AAAE,QAAG,KAAK,YAAW;AAAC,YAAMJ,KAAE;AAAG,MAAAK,KAAEL,KAAEO,KAAE,KAAK,gBAAgB,cAAYP,KAAEK,KAAEL,KAAEM,OAAI,KAAK,gBAAgB,cAAYN;AAAA,IAAE,OAAK;AAAC,OAACK,KAAEC,MAAGD,KAAEE,QAAK,KAAK,gBAAgB,aAAWF,MAAG,KAAK,QAAQ,aAAWG,KAAE;AAAI,YAAMR,KAAEK,KAAEH,KAAEM;AAAE,MAAAP,MAAG,KAAK,QAAQ,cAAYD,KAAE,MAAI,KAAK,gBAAgB,cAAY,KAAK,IAAIA,IAAE,EAAE;AAAA,IAAE;AAAC;AAAC,YAAMA,KAAE,KAAK,gBAAgB,YAAWC,KAAED,KAAEG,IAAED,MAAGF,KAAEI,MAAGD;AAAE,WAAK,KAAK,UAASF,IAAEC,IAAEF,IAAEA,KAAEI,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeJ,IAAEC,IAAE;AAAC,QAAG,MAAMD,EAAC,EAAE;AAAO,UAAME,KAAE,MAAIF;AAAE,SAAK,cAAc,MAAM,WAAS,WAAWE,EAAC,6BAA6BA,EAAC,WAAU,KAAK,gBAAgB,MAAM,QAAM,GAAGA,EAAC,KAAI,KAAK,OAAO,MAAM,OAAK,GAAGA,EAAC,KAAI,KAAK,OAAO,MAAM,YAAU,eAAe,QAAM,KAAK,MAAMA,EAAC,IAAE,KAAK,QAAQ,cAAY,CAAC,OAAM,KAAK,gBAAc,KAAK,QAAQ,cAAY,KAAK,eAAeF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,YAAMH,KAAE,KAAK,cAAc,iBAAiB,QAAQ;AAAE,UAAG,CAACA,GAAE,OAAO,OAAM,IAAI,MAAM,kBAAkB;AAAE,UAAG,cAAYG,IAAE;AAAC,cAAMA,KAAE,MAAM,KAAKH,EAAC,EAAE,IAAK,CAAAA,OAAGA,GAAE,UAAUC,IAAEC,EAAC,CAAE;AAAE,eAAO,QAAQ,QAAQC,EAAC;AAAA,MAAC;AAAC,aAAO,QAAQ,IAAI,MAAM,KAAKH,EAAC,EAAE,IAAK,CAAAA,OAAG,IAAI,QAAS,CAACG,IAAEC,OAAI;AAAC,QAAAJ,GAAE,OAAQ,CAAAA,OAAG;AAAC,UAAAA,KAAEG,GAAEH,EAAC,IAAEI,GAAE,IAAI,MAAM,wBAAwB,CAAC;AAAA,QAAC,GAAGH,IAAEC,EAAC;AAAA,MAAC,CAAE,CAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAC,EAAE,mBAAiB,KAAI,EAAE,YAAU;AAAG,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,MAAI;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,cAAY,KAAK,GAAG,QAAQ,MAAI;AAAC,4BAAuB,MAAI;AAAC,aAAK,KAAK,MAAM;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,KAAK,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,YAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYF,KAAE,IAAI,gBAAa;AAAC,UAAM,GAAE,KAAK,aAAW,MAAK,KAAK,gBAAc,GAAE,KAAK,iBAAe,GAAE,KAAK,SAAO,OAAG,KAAK,gBAAc,GAAE,KAAK,YAAU,QAAO,KAAK,SAAO,MAAK,KAAK,aAAW,IAAG,KAAK,SAAO,MAAG,KAAK,cAAY,MAAK,KAAK,UAAQ,OAAG,KAAK,WAAS,OAAG,KAAK,mBAAiB,KAAK,IAAG,KAAK,sBAAoB,KAAK,IAAG,KAAK,eAAaA,IAAE,KAAK,WAAS,KAAK,aAAa,WAAW,GAAE,KAAK,SAAS,QAAQ,KAAK,aAAa,WAAW;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,IAAIA,IAAE;AAAC,QAAG,KAAK,aAAWA,IAAE,KAAK,YAAU,QAAO,CAACA,GAAE,QAAO,KAAK,SAAO,MAAK,KAAK,KAAK,KAAK,SAAS;AAAE,UAAMA,EAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAGA,GAAE,UAAQ,IAAI,OAAM,IAAI,MAAM,mBAAmBD,EAAC,KAAKC,GAAE,MAAM,KAAKA,GAAE,UAAU,GAAG;AAAE,aAAOA,GAAE,YAAY;AAAA,IAAC,CAAE,EAAE,KAAM,CAAAA,OAAG,KAAK,eAAaD,KAAE,OAAK,KAAK,aAAa,gBAAgBC,EAAC,CAAE,EAAE,KAAM,CAAAA,OAAG;AAAC,WAAK,eAAaD,OAAI,KAAK,SAAOC,IAAE,KAAK,KAAK,gBAAgB,GAAE,KAAK,KAAK,SAAS,GAAE,KAAK,YAAU,KAAK,KAAK;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAID;AAAE,QAAG,CAAC,KAAK,OAAO;AAAO,SAAK,SAAO,OAAG,UAAQA,KAAE,KAAK,eAAa,WAASA,MAAGA,GAAE,WAAW,GAAE,KAAK,aAAW,KAAK,aAAa,mBAAmB,GAAE,KAAK,WAAS,KAAK,WAAW,SAAO,KAAK,SAAQ,KAAK,WAAW,aAAa,QAAM,KAAK,eAAc,KAAK,WAAW,QAAQ,KAAK,QAAQ;AAAE,QAAIC,KAAE,KAAK,iBAAe,KAAK;AAAc,KAACA,MAAG,KAAK,YAAUA,KAAE,OAAKA,KAAE,GAAE,KAAK,iBAAe,IAAG,KAAK,WAAW,MAAM,KAAK,aAAa,aAAYA,EAAC,GAAE,KAAK,gBAAc,KAAK,aAAa,aAAY,KAAK,WAAW,UAAQ,MAAI;AAAC,WAAK,eAAa,KAAK,aAAW,KAAK,MAAM,GAAE,KAAK,KAAK,OAAO;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAID;AAAE,SAAK,SAAO,MAAG,UAAQA,KAAE,KAAK,eAAa,WAASA,MAAGA,GAAE,KAAK,GAAE,KAAK,kBAAgB,KAAK,aAAa,cAAY,KAAK;AAAA,EAAa;AAAA,EAAC,OAAM;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,WAAK,WAAS,KAAK,MAAM,GAAE,KAAK,KAAK,MAAM;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,WAAS,KAAK,OAAO,GAAE,KAAK,KAAK,OAAO;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAMC,KAAED,KAAE,KAAK,aAAYE,KAAE,KAAK;AAAW,YAAMA,MAAGA,GAAE,KAAK,KAAK,aAAa,cAAYD,EAAC,GAAE,QAAMC,MAAGA,GAAE,iBAAiB,SAAS,MAAI;AAAC,MAAAA,OAAI,KAAK,eAAa,KAAK,aAAW,MAAK,KAAK,MAAM;AAAA,IAAE,GAAG,EAAC,MAAK,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,aAAO,KAAK,aAAa,UAAUA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,aAAaD,IAAE;AAAC,SAAK,gBAAcA,IAAE,KAAK,eAAa,KAAK,WAAW,aAAa,QAAMA;AAAA,EAAE;AAAA,EAAC,IAAI,cAAa;AAAC,YAAO,KAAK,SAAO,KAAK,iBAAe,KAAK,kBAAgB,KAAK,aAAa,cAAY,KAAK,kBAAgB,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,UAAMC,KAAE,CAAC,KAAK;AAAO,IAAAA,MAAG,KAAK,OAAO,GAAE,KAAK,iBAAeD,KAAE,KAAK,eAAcC,MAAG,KAAK,MAAM,GAAE,KAAK,KAAK,SAAS,GAAE,KAAK,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,QAAID,IAAEC;AAAE,WAAO,UAAQD,KAAE,KAAK,cAAY,WAASA,KAAEA,MAAG,UAAQC,KAAE,KAAK,WAAS,WAASA,KAAE,SAAOA,GAAE,aAAW;AAAA,EAAC;AAAA,EAAC,IAAI,SAASD,IAAE;AAAC,SAAK,YAAUA;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,SAAS,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,SAAK,SAAS,KAAK,QAAMA,IAAE,KAAK,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,WAASA,OAAI,KAAK,SAAOA,IAAE,KAAK,SAAO,KAAK,SAAS,WAAW,IAAE,KAAK,SAAS,QAAQ,KAAK,aAAa,WAAW;AAAA,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAM,mBAAmB,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,iBAAgB;AAAC,UAAMA,KAAE,CAAC;AAAE,QAAG,CAAC,KAAK,OAAO,QAAOA;AAAE,UAAMC,KAAE,KAAK,OAAO;AAAiB,aAAQC,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAF,GAAE,KAAK,KAAK,OAAO,eAAeE,EAAC,CAAC;AAAE,WAAOF;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAC,WAAU,QAAO,eAAc,QAAO,aAAY,GAAE,aAAY,GAAE,YAAW,MAAG,UAAS,MAAG,YAAW,OAAG,YAAW,MAAG,YAAW,MAAG,YAAW,IAAG;AAAE,IAAM,IAAN,MAAM,WAAU,EAAC;AAAA,EAAC,OAAO,OAAOA,IAAE;AAAC,WAAO,IAAI,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAED,GAAE,UAAQ,eAAaA,GAAE,UAAQ,IAAI,MAAE;AAAQ,UAAM,EAAC,OAAMC,IAAE,eAAcD,GAAE,eAAc,UAASA,GAAE,UAAS,cAAaA,GAAE,UAAS,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,cAAY,MAAK,KAAK,iBAAe,MAAK,KAAK,gBAAc,CAAC,GAAE,KAAK,qBAAmB,CAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,UAAQ,OAAO,OAAO,CAAC,GAAE,GAAEA,EAAC,GAAE,KAAK,QAAM,IAAI;AAAE,UAAME,KAAED,KAAE,SAAO,KAAK,gBAAgB;AAAE,SAAK,WAAS,IAAI,EAAE,KAAK,SAAQC,EAAC,GAAE,KAAK,iBAAiB,GAAE,KAAK,mBAAmB,GAAE,KAAK,gBAAgB,GAAE,KAAK,YAAY;AAAE,UAAMC,KAAE,KAAK,QAAQ,OAAK,KAAK,OAAO,KAAG;AAAG,YAAQ,QAAQ,EAAE,KAAM,MAAI;AAAC,WAAK,KAAK,MAAM;AAAE,YAAK,EAAC,OAAMH,IAAE,UAASC,GAAC,IAAE,KAAK;AAAQ,OAACE,MAAGH,MAAGC,OAAI,KAAK,KAAKE,IAAEH,IAAEC,EAAC,EAAE,MAAO,MAAI,IAAK;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,KAAE,KAAK,eAAe,GAAE;AAAC,WAAO,KAAK,SAAS,eAAeA,KAAE,KAAK,YAAY,GAAE,KAAK,UAAU,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,SAAK,cAAc,KAAK,KAAK,MAAM,GAAG,QAAQ,MAAI;AAAC,UAAG,CAAC,KAAK,UAAU,GAAE;AAAC,cAAMA,KAAE,KAAK,eAAe;AAAE,aAAK,KAAK,cAAaA,EAAC,GAAE,KAAK,KAAK,gBAAeA,EAAC,GAAE,QAAM,KAAK,kBAAgB,KAAK,UAAU,KAAGA,MAAG,KAAK,kBAAgB,KAAK,MAAM;AAAA,MAAC;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,UAAU,MAAI,KAAK,KAAK,MAAM,GAAE,KAAK,MAAM,MAAM,IAAG,KAAK,mBAAmB,KAAK,KAAK,aAAa,cAAc,MAAI;AAAC,YAAMA,KAAE,KAAK,eAAe;AAAE,WAAK,KAAK,cAAaA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,aAAa,QAAQ,MAAI;AAAC,WAAK,KAAK,MAAM,GAAE,KAAK,MAAM,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,aAAa,SAAS,MAAI;AAAC,WAAK,KAAK,OAAO,GAAE,KAAK,MAAM,KAAK,GAAE,KAAK,iBAAe;AAAA,IAAI,CAAE,GAAE,KAAK,aAAa,WAAW,MAAI;AAAC,WAAK,MAAM,KAAK,GAAE,KAAK,iBAAe;AAAA,IAAI,CAAE,GAAE,KAAK,aAAa,SAAS,MAAI;AAAC,WAAK,KAAK,cAAa,KAAK,YAAY,CAAC,GAAE,KAAK,KAAK,QAAQ,GAAE,KAAK,iBAAe;AAAA,IAAI,CAAE,GAAE,KAAK,aAAa,WAAW,MAAI;AAAC,WAAK,KAAK,WAAU,KAAK,eAAe,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,aAAa,SAAS,MAAI;AAAC,UAAIA;AAAE,WAAK,KAAK,SAAQ,UAAQA,KAAE,KAAK,gBAAgB,EAAE,UAAQ,WAASA,KAAEA,KAAE,IAAI,MAAM,aAAa,CAAC,GAAE,KAAK,iBAAe;AAAA,IAAI,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,cAAc,KAAK,KAAK,SAAS,GAAG,SAAS,CAACA,IAAEC,OAAI;AAAC,WAAK,QAAQ,aAAW,KAAK,OAAOD,EAAC,GAAE,KAAK,KAAK,eAAcA,KAAE,KAAK,YAAY,CAAC,GAAE,KAAK,KAAK,SAAQA,IAAEC,EAAC;AAAA,IAAE,CAAE,GAAE,KAAK,SAAS,GAAG,YAAY,CAACD,IAAEC,OAAI;AAAC,WAAK,KAAK,YAAWD,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,GAAG,UAAU,CAACD,IAAEC,IAAEC,IAAEC,OAAI;AAAC,YAAMC,KAAE,KAAK,YAAY;AAAE,WAAK,KAAK,UAASJ,KAAEI,IAAEH,KAAEG,IAAEF,IAAEC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,GAAG,UAAU,MAAI;AAAC,WAAK,KAAK,QAAQ;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,GAAG,YAAY,MAAI;AAAC,WAAK,KAAK,gBAAgB;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,GAAG,aAAa,CAAAH,OAAG;AAAC,WAAK,KAAK,aAAYA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,GAAG,WAAW,CAAAA,OAAG;AAAC,WAAK,KAAK,WAAUA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAE;AAAC,UAAIA;AAAE,WAAK,cAAc,KAAK,KAAK,SAAS,GAAG,QAAQ,CAAAC,OAAG;AAAC,YAAG,CAAC,KAAK,QAAQ,SAAS;AAAO,YAAIC;AAAE,aAAK,SAAS,eAAeD,EAAC,GAAE,aAAaD,EAAC,GAAE,KAAK,UAAU,IAAEE,KAAE,IAAE,SAAK,KAAK,QAAQ,aAAWA,KAAE,MAAI,YAAU,OAAO,KAAK,QAAQ,cAAY,WAAS,KAAK,QAAQ,eAAaA,KAAE,KAAK,QAAQ,WAAW,eAAcF,KAAE,WAAY,MAAI;AAAC,eAAK,OAAOC,EAAC;AAAA,QAAC,GAAGC,EAAC,GAAE,KAAK,KAAK,eAAcD,KAAE,KAAK,YAAY,CAAC,GAAE,KAAK,KAAK,QAAOA,EAAC;AAAA,MAAC,CAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,QAAID;AAAE,KAAC,UAAQA,KAAE,KAAK,QAAQ,YAAU,WAASA,KAAE,SAAOA,GAAE,WAAS,KAAK,QAAQ,QAAQ,QAAS,CAAAA,OAAG;AAAC,WAAK,eAAeA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,mBAAmB,QAAS,CAAAA,OAAGA,GAAE,CAAE,GAAE,KAAK,qBAAmB,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,UAAQ,OAAO,OAAO,CAAC,GAAE,KAAK,SAAQA,EAAC,GAAEA,GAAE,YAAU,CAACA,GAAE,UAAQ,KAAK,cAAY,EAAE,aAAa,KAAK,YAAY,GAAEA,GAAE,QAAQ,IAAGA,GAAE,SAAOA,GAAE,aAAW,KAAK,cAAY,EAAE,aAAaA,GAAE,OAAMA,GAAE,QAAQ,IAAG,KAAK,SAAS,WAAW,KAAK,OAAO,GAAEA,GAAE,aAAW,KAAK,gBAAgBA,GAAE,SAAS,GAAE,QAAMA,GAAE,kBAAgB,KAAK,gBAAgB,EAAE,WAASA,GAAE;AAAA,EAAc;AAAA,EAAC,eAAeA,IAAE;AAAC,IAAAA,GAAE,MAAM,IAAI,GAAE,KAAK,QAAQ,KAAKA,EAAC;AAAE,UAAMC,KAAED,GAAE,KAAK,WAAW,MAAI;AAAC,WAAK,UAAQ,KAAK,QAAQ,OAAQ,CAAAC,OAAGA,OAAID,EAAE,GAAE,KAAK,gBAAc,KAAK,cAAc,OAAQ,CAAAA,OAAGA,OAAIC,EAAE;AAAA,IAAC,CAAE;AAAE,WAAO,KAAK,cAAc,KAAKA,EAAC,GAAED;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,SAAS,WAAW;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK,SAAS,SAAS;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,SAAS,UAAU;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAO,KAAK,SAAS,UAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,UAAMC,KAAED,KAAE,KAAK,YAAY;AAAE,SAAK,SAAS,oBAAoBC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,UAAUA,IAAEE,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAIL;AAAE,UAAG,KAAK,KAAK,QAAOC,EAAC,GAAE,CAAC,KAAK,QAAQ,SAAO,KAAK,UAAU,KAAG,KAAK,MAAM,GAAE,KAAK,cAAY,MAAK,KAAK,iBAAe,MAAK,CAACE,MAAG,CAACC,IAAE;AAAC,cAAMF,KAAE,KAAK,QAAQ,eAAa,CAAC;AAAE,eAAO,mBAAiB,CAACA,GAAE,WAAS,KAAK,kBAAgB,IAAI,mBAAgBA,GAAE,SAAO,UAAQF,KAAE,KAAK,oBAAkB,WAASA,KAAE,SAAOA,GAAE;AAAQ,cAAMI,KAAE,CAAAJ,OAAG,KAAK,KAAK,WAAUA,EAAC;AAAE,QAAAG,KAAE,MAAM,EAAE,UAAUF,IAAEG,IAAEF,EAAC;AAAE,cAAMG,KAAE,KAAK,QAAQ;AAAa,QAAAA,OAAIF,KAAE,IAAI,KAAK,CAACA,EAAC,GAAE,EAAC,MAAKE,GAAC,CAAC;AAAA,MAAE;AAAC,YAAIJ,KAAE,KAAK,gBAAgB,EAAE,gBAAgB,KAAK,IAAE,KAAK,OAAOA,IAAEE,EAAC;AAAE,YAAMI,KAAE,MAAM,IAAI,QAAS,CAAAP,OAAG;AAAC,cAAMC,KAAEI,MAAG,KAAK,YAAY;AAAE,QAAAJ,KAAED,GAAEC,EAAC,IAAE,KAAK,mBAAmB,KAAK,KAAK,aAAa,kBAAkB,MAAID,GAAE,KAAK,YAAY,CAAC,GAAG,EAAC,MAAK,KAAE,CAAC,CAAC;AAAA,MAAC,CAAE;AAAE,UAAG,CAACC,MAAG,CAACE,IAAE;AAAC,cAAMH,KAAE,KAAK,gBAAgB;AAAE,QAAAA,cAAa,MAAIA,GAAE,WAASO;AAAA,MAAE;AAAC,UAAGH,GAAE,MAAK,cAAY,EAAE,aAAaA,IAAEG,MAAG,CAAC;AAAA,eAAUJ,IAAE;AAAC,cAAMH,KAAE,MAAMG,GAAE,YAAY;AAAE,aAAK,cAAY,MAAM,EAAE,OAAOH,IAAE,KAAK,QAAQ,UAAU;AAAA,MAAC;AAAC,WAAK,gBAAc,KAAK,KAAK,UAAS,KAAK,YAAY,CAAC,GAAE,KAAK,SAAS,OAAO,KAAK,WAAW,IAAG,KAAK,KAAK,SAAQ,KAAK,YAAY,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAG;AAAC,eAAO,MAAM,KAAK,UAAUF,IAAE,QAAOC,IAAEC,EAAC;AAAA,MAAC,SAAOH,IAAE;AAAC,cAAM,KAAK,KAAK,SAAQA,EAAC,GAAEA;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAASC,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,UAAG;AAAC,eAAO,MAAM,KAAK,UAAU,IAAGF,IAAEC,IAAEC,EAAC;AAAA,MAAC,SAAOH,IAAE;AAAC,cAAM,KAAK,KAAK,SAAQA,EAAC,GAAEA;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,QAAG,CAAC,KAAK,YAAY,OAAM,IAAI,MAAM,iBAAiB;AAAE,SAAK,SAAS,KAAKA,EAAC,GAAE,KAAK,KAAK,QAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,YAAY,EAAC,UAASA,KAAE,GAAE,WAAUC,KAAE,KAAI,WAAUC,KAAE,IAAG,IAAE,CAAC,GAAE;AAAC,QAAG,CAAC,KAAK,YAAY,OAAM,IAAI,MAAM,oCAAoC;AAAE,UAAMC,KAAE,KAAK,IAAIH,IAAE,KAAK,YAAY,gBAAgB,GAAEI,KAAE,CAAC;AAAE,aAAQJ,KAAE,GAAEA,KAAEG,IAAEH,MAAI;AAAC,YAAMG,KAAE,KAAK,YAAY,eAAeH,EAAC,GAAEK,KAAE,CAAC,GAAEC,KAAEH,GAAE,SAAOF;AAAE,eAAQD,KAAE,GAAEA,KAAEC,IAAED,MAAI;AAAC,cAAMC,KAAEE,GAAE,MAAM,KAAK,MAAMH,KAAEM,EAAC,GAAE,KAAK,MAAMN,KAAE,KAAGM,EAAC,CAAC;AAAE,YAAIF,KAAE;AAAE,iBAAQJ,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,gBAAME,KAAED,GAAED,EAAC;AAAE,eAAK,IAAIE,EAAC,IAAE,KAAK,IAAIE,EAAC,MAAIA,KAAEF;AAAA,QAAE;AAAC,QAAAG,GAAE,KAAK,KAAK,MAAMD,KAAEF,EAAC,IAAEA,EAAC;AAAA,MAAC;AAAC,MAAAE,GAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,QAAIJ,KAAE,MAAM,YAAY,KAAG;AAAE,WAAO,MAAIA,MAAGA,OAAI,IAAE,KAAG,CAAC,KAAK,gBAAcA,KAAE,KAAK,YAAY,WAAUA;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,SAAK,QAAQ,WAASA;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,iBAAe,MAAK,MAAM,QAAQA,EAAC,GAAE,KAAK,eAAeA,EAAC,GAAE,KAAK,KAAK,cAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAY,IAAED;AAAE,SAAK,QAAQC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAEC,IAAE;AAAC,UAAMC,KAAE,OAAO,OAAO,MAAK,EAAC,MAAK,EAAC,KAAI,MAAI,MAAM,KAAI,EAAC,CAAC;AAAE,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,cAAMF,MAAG,KAAK,QAAQA,EAAC;AAAE,YAAMD,KAAE,MAAMG,GAAE,KAAK,KAAK,IAAI;AAAE,aAAO,QAAMD,OAAI,KAAK,iBAAiB,IAAE,KAAK,MAAM,OAAOA,EAAC,IAAE,KAAK,iBAAeA,KAAGF;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,aAAO,KAAK,UAAU,IAAE,KAAK,MAAM,IAAE,KAAK,KAAK;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,MAAM,GAAE,KAAK,QAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,SAAK,QAAQ,KAAK,eAAe,IAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAK,IAAG,CAAC,CAAC,CAAC,CAAC,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,SAAK,wBAAwB,GAAE,MAAM,gBAAgBA,EAAC,GAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,EAAE,MAAK,WAAU,QAAQ,WAAUA,KAAE,aAAYC,KAAE,GAAEC,KAAE,WAAU;AAAC,aAAO,KAAK,SAAS,YAAYF,IAAEC,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAIF;AAAE,SAAK,KAAK,SAAS,GAAE,UAAQA,KAAE,KAAK,oBAAkB,WAASA,MAAGA,GAAE,MAAM,GAAE,KAAK,QAAQ,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,cAAc,QAAS,CAAAA,OAAGA,GAAE,CAAE,GAAE,KAAK,wBAAwB,GAAE,KAAK,MAAM,QAAQ,GAAE,KAAK,SAAS,QAAQ,GAAE,MAAM,QAAQ;AAAA,EAAC;AAAC;AAAC,EAAE,aAAW,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAE;AAAC,SAAK,aAAWA,IAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,SAAS,GAAE,KAAK,cAAc,QAAS,CAAAA,OAAGA,GAAE,CAAE;AAAA,EAAC;AAAC,GAAE,EAAE,MAAI;", "names": ["t", "e", "i", "s", "n", "r", "o", "h", "a", "t", "e", "i", "s", "n", "r", "o", "a", "h", "l", "d", "c", "u", "m"]}